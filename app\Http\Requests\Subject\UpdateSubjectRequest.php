<?php

namespace App\Http\Requests\Subject;

use Illuminate\Foundation\Http\FormRequest;

class UpdateSubjectRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true; // Adjust based on authorization logic
    }

    /**
     * Get the validation rules that apply to the request.
     */
    public function rules(): array
    {
        $subjectId = $this->route('subject')?->id;

        return [
            'name' => [
                'required',
                'string',
                'max:100',
            ],
            'code' => [
                'required',
                'string',
                'max:20',
                'unique:subjects,code,' . $subjectId,
                'regex:/^[A-Z0-9_-]+$/',
            ],
            'credit_hours' => [
                'required',
                'integer',
                'min:1',
                'max:10',
            ],
            'description' => [
                'nullable',
                'string',
            ],
            'status' => [
                'required',
                'in:active,inactive',
            ],
        ];
    }

    /**
     * Get custom messages for validator errors.
     */
    public function messages(): array
    {
        return [
            'name.required' => 'Subject name is required.',
            'name.max' => 'Subject name cannot exceed 100 characters.',
            'code.required' => 'Subject code is required.',
            'code.unique' => 'This subject code is already taken.',
            'code.regex' => 'Subject code can only contain uppercase letters, numbers, underscores, and hyphens.',
            'credit_hours.required' => 'Credit hours is required.',
            'credit_hours.integer' => 'Credit hours must be a whole number.',
            'credit_hours.min' => 'Credit hours must be at least 1.',
            'credit_hours.max' => 'Credit hours cannot exceed 10.',
            'status.required' => 'Status is required.',
            'status.in' => 'Please select a valid status.',
        ];
    }

    /**
     * Get custom attributes for validator errors.
     */
    public function attributes(): array
    {
        return [
            'name' => 'subject name',
            'code' => 'subject code',
            'credit_hours' => 'credit hours',
        ];
    }
}
