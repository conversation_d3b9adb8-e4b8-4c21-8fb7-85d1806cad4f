<?php

namespace App\Enums;

enum Religion: string
{
    case ISLAM = 'islam';
    case KRISTEN = 'kristen';
    case KATOLIK = 'katolik';
    case HINDU = 'hindu';
    case BUDDHA = 'buddha';
    case KONGHUCU = 'konghu<PERSON>';

    public function label(): string
    {
        return match($this) {
            self::ISLAM => 'Islam',
            self::KRISTEN => 'Kristen',
            self::KATOLIK => 'Katolik',
            self::HINDU => 'Hindu',
            self::BUDDHA => 'Buddha',
            self::KONGHUCU => 'Konghu<PERSON>',
        };
    }

    public static function options(): array
    {
        return [
            self::ISLAM->value => self::ISLAM->label(),
            self::KRISTEN->value => self::KRISTEN->label(),
            self::KATOLIK->value => self::KATOLIK->label(),
            self::HINDU->value => self::HINDU->label(),
            self::BUDDHA->value => self::BUDDHA->label(),
            self::KONGHUCU->value => self::KONGHUCU->label(),
        ];
    }
}
