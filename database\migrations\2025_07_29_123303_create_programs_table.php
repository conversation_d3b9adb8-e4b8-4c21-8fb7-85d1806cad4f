<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('programs', function (Blueprint $table) {
            $table->id();
            $table->string('name', 100);
            $table->string('code', 20)->unique();
            $table->enum('education_level', ['SD', 'SMP', 'SMA', 'D3', 'S1', 'S2', 'S3']);
            $table->text('description')->nullable();
            $table->unsignedTinyInteger('duration_years')->default(1);
            $table->enum('status', ['active', 'inactive', 'archived'])->default('active');
            $table->timestamps();

            // Indexes
            $table->index('education_level');
            $table->index('status');
            $table->index('code');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('programs');
    }
};
