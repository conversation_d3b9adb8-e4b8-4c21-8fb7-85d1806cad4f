<?php

namespace App\Models;

use App\Enums\Gender;
use App\Enums\Religion;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use Illuminate\Database\Eloquent\Relations\MorphMany;

class ParentModel extends Model
{
    protected $table = 'parents';

    protected $fillable = [
        'first_name',
        'last_name',
        'gender',
        'birth_date',
        'religion',
        'occupation',
        'monthly_income',
        'notes'
    ];

    protected $casts = [
        'gender' => Gender::class,
        'religion' => Religion::class,
        'birth_date' => 'date',
        'monthly_income' => 'decimal:2',
    ];

    public function contactInformation(): MorphMany
    {
        return $this->morphMany(ContactInformation::class, 'contactable');
    }

    public function students(): BelongsToMany
    {
        return $this->belongsToMany(Student::class, 'student_parents', 'parent_id', 'student_id')
            ->withPivot('relation_type', 'is_emergency_contact', 'notes')
            ->withTimestamps();
    }

    public function getFullNameAttribute(): string
    {
        return trim($this->first_name . ' ' . $this->last_name);
    }
}
