# Laravel Controllers and Services for `db_rawooh_v2`

Below are the CRUD controllers and their corresponding services for `Users`, `Teachers`, `Students`, `Classrooms`, and `TeacherAttendances`. The implementation follows Laravel best practices with clear naming conventions and separation of concerns.

---

## Directory Structure
- Controllers: `app/Http/Controllers`
- Services: `app/Services`
- Requests: `app/Http/Requests`
- Models: `app/Models` (assumed to exist as provided previously)

---

## Controllers

### 1. `UserController.php`
```php
<?php

namespace App\Http\Controllers;

use App\Http\Requests\User\StoreUserRequest;
use App\Http\Requests\User\UpdateUserRequest;
use App\Models\User;
use App\Services\User\CreateUserService;
use App\Services\User\UpdateUserService;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;

class UserController extends Controller
{
    protected $createUserService;
    protected $updateUserService;

    public function __construct(CreateUserService $createUserService, UpdateUserService $updateUserService)
    {
        $this->createUserService = $createUserService;
        $this->updateUserService = $updateUserService;
    }

    public function index(): JsonResponse
    {
        $users = User::with(['teacher', 'student', 'parent'])->paginate(10);
        return response()->json([
            'message' => 'Users retrieved successfully',
            'data' => $users,
        ], 200);
    }

    public function store(StoreUserRequest $request): JsonResponse
    {
        try {
            $user = $this->createUserService->execute($request->validated());
            return response()->json([
                'message' => 'User created successfully',
                'data' => $user->load(['teacher', 'student', 'parent']),
            ], 201);
        } catch (\Exception $e) {
            return response()->json([
                'message' => 'Failed to create user',
                'error' => $e->getMessage(),
            ], 500);
        }
    }

    public function show(User $user): JsonResponse
    {
        return response()->json([
            'message' => 'User retrieved successfully',
            'data' => $user->load(['teacher', 'student', 'parent']),
        ], 200);
    }

    public function update(UpdateUserRequest $request, User $user): JsonResponse
    {
        try {
            $user = $this->updateUserService->execute($user, $request->validated());
            return response()->json([
                'message' => 'User updated successfully',
                'data' => $user->load(['teacher', 'student', 'parent']),
            ], 200);
        } catch (\Exception $e) {
            return response()->json([
                'message' => 'Failed to update user',
                'error' => $e->getMessage(),
            ], 500);
        }
    }

    public function destroy(User $user): JsonResponse
    {
        try {
            $user->delete();
            return response()->json([
                'message' => 'User deleted successfully',
            ], 200);
        } catch (\Exception $e) {
            return response()->json([
                'message' => 'Failed to delete user',
                'error' => $e->getMessage(),
            ], 500);
        }
    }
}
```

### 2. `TeacherController.php`
```php
<?php

namespace App\Http\Controllers;

use App\Http\Requests\Teacher\StoreTeacherRequest;
use App\Http\Requests\Teacher\UpdateTeacherRequest;
use App\Models\Teacher;
use App\Services\Teacher\CreateTeacherService;
use App\Services\Teacher\UpdateTeacherService;
use Illuminate\Http\JsonResponse;

class TeacherController extends Controller
{
    protected $createTeacherService;
    protected $updateTeacherService;

    public function __construct(CreateTeacherService $createTeacherService, UpdateTeacherService $updateTeacherService)
    {
        $this->createTeacherService = $createTeacherService;
        $this->updateTeacherService = $updateTeacherService;
    }

    public function index(): JsonResponse
    {
        $teachers = Teacher::with('user')->paginate(10);
        return response()->json([
            'message' => 'Teachers retrieved successfully',
            'data' => $teachers,
        ], 200);
    }

    public function store(StoreTeacherRequest $request): JsonResponse
    {
        try {
            $teacher = $this->createTeacherService->execute($request->validated());
            return response()->json([
                'message' => 'Teacher created successfully',
                'data' => $teacher->load('user'),
            ], 201);
        } catch (\Exception $e) {
            return response()->json([
                'message' => 'Failed to create teacher',
                'error' => $e->getMessage(),
            ], 500);
        }
    }

    public function show(Teacher $teacher): JsonResponse
    {
        return response()->json([
            'message' => 'Teacher retrieved successfully',
            'data' => $teacher->load('user'),
        ], 200);
    }

    public function update(UpdateTeacherRequest $request, Teacher $teacher): JsonResponse
    {
        try {
            $teacher = $this->updateTeacherService->execute($teacher, $request->validated());
            return response()->json([
                'message' => 'Teacher updated successfully',
                'data' => $teacher->load('user'),
            ], 200);
        } catch (\Exception $e) {
            return response()->json([
                'message' => 'Failed to update teacher',
                'error' => $e->getMessage(),
            ], 500);
        }
    }

    public function destroy(Teacher $teacher): JsonResponse
    {
        try {
            $teacher->delete();
            return response()->json([
                'message' => 'Teacher deleted successfully',
            ], 200);
        } catch (\Exception $e) {
            return response()->json([
                'message' => 'Failed to delete teacher',
                'error' => $e->getMessage(),
            ], 500);
        }
    }
}
```

### 3. `StudentController.php`
```php
<?php

namespace App\Http\Controllers;

use App\Http\Requests\Student\StoreStudentRequest;
use App\Http\Requests\Student\UpdateStudentRequest;
use App\Models\Student;
use App\Services\Student\CreateStudentService;
use App\Services\Student\UpdateStudentService;
use Illuminate\Http\JsonResponse;

class StudentController extends Controller
{
    protected $createStudentService;
    protected $updateStudentService;

    public function __construct(CreateStudentService $createStudentService, UpdateStudentService $updateStudentService)
    {
        $this->createStudentService = $createStudentService;
        $this->updateStudentService = $updateStudentService;
    }

    public function index(): JsonResponse
    {
        $students = Student::with('user')->paginate(10);
        return response()->json([
            'message' => 'Students retrieved successfully',
            'data' => $students,
        ], 200);
    }

    public function store(StoreStudentRequest $request): JsonResponse
    {
        try {
            $student = $this->createStudentService->execute($request->validated());
            return response()->json([
                'message' => 'Student created successfully',
                'data' => $student->load('user'),
            ], 201);
        } catch (\Exception $e) {
            return response()->json([
                'message' => 'Failed to create student',
                'error' => $e->getMessage(),
            ], 500);
        }
    }

    public function show(Student $student): JsonResponse
    {
        return response()->json([
            'message' => 'Student retrieved successfully',
            'data' => $student->load('user'),
        ], 200);
    }

    public function update(UpdateStudentRequest $request, Student $student): JsonResponse
    {
        try {
            $student = $this->updateStudentService->execute($student, $request->validated());
            return response()->json([
                'message' => 'Student updated successfully',
                'data' => $student->load('user'),
            ], 200);
        } catch (\Exception $e) {
            return response()->json([
                'message' => 'Failed to update student',
                'error' => $e->getMessage(),
            ], 500);
        }
    }

    public function destroy(Student $student): JsonResponse
    {
        try {
            $student->delete();
            return response()->json([
                'message' => 'Student deleted successfully',
            ], 200);
        } catch (\Exception $e) {
            return response()->json([
                'message' => 'Failed to delete student',
                'error' => $e->getMessage(),
            ], 500);
        }
    }
}
```

### 4. `ClassroomController.php`
```php
<?php

namespace App\Http\Controllers;

use App\Http\Requests\Classroom\StoreClassroomRequest;
use App\Http\Requests\Classroom\UpdateClassroomRequest;
use App\Models\Classroom;
use App\Services\Classroom\CreateClassroomService;
use App\Services\Classroom\UpdateClassroomService;
use Illuminate\Http\JsonResponse;

class ClassroomController extends Controller
{
    protected $createClassroomService;
    protected $updateClassroomService;

    public function __construct(CreateClassroomService $createClassroomService, UpdateClassroomService $updateClassroomService)
    {
        $this->createClassroomService = $createClassroomService;
        $this->updateClassroomService = $updateClassroomService;
    }

    public function index(): JsonResponse
    {
        $classrooms = Classroom::with(['program', 'shift', 'teacher', 'academicYear'])->paginate(10);
        return response()->json([
            'message' => 'Classrooms retrieved successfully',
            'data' => $classrooms,
        ], 200);
    }

    public function store(StoreClassroomRequest $request): JsonResponse
    {
        try {
            $classroom = $this->createClassroomService->execute($request->validated());
            return response()->json([
                'message' => 'Classroom created successfully',
                'data' => $classroom->load(['program', 'shift', 'teacher', 'academicYear']),
            ], 201);
        } catch (\Exception $e) {
            return response()->json([
                'message' => 'Failed to create classroom',
                'error' => $e->getMessage(),
            ], 500);
        }
    }

    public function show(Classroom $classroom): JsonResponse
    {
        return response()->json([
            'message' => 'Classroom retrieved successfully',
            'data' => $classroom->load(['program', 'shift', 'teacher', 'academicYear']),
        ], 200);
    }

    public function update(UpdateClassroomRequest $request, Classroom $classroom): JsonResponse
    {
        try {
            $classroom = $this->updateClassroomService->execute($classroom, $request->validated());
            return response()->json([
                'message' => 'Classroom updated successfully',
                'data' => $classroom->load(['program', 'shift', 'teacher', 'academicYear']),
            ], 200);
        } catch (\Exception $e) {
            return response()->json([
                'message' => 'Failed to update classroom',
                'error' => $e->getMessage(),
            ], 500);
        }
    }

    public function destroy(Classroom $classroom): JsonResponse
    {
        try {
            $classroom->delete();
            return response()->json([
                'message' => 'Classroom deleted successfully',
            ], 200);
        } catch (\Exception $e) {
            return response()->json([
                'message' => 'Failed to delete classroom',
                'error' => $e->getMessage(),
            ], 500);
        }
    }
}
```

### 5. `TeacherAttendanceController.php`
```php
<?php

namespace App\Http\Controllers;

use App\Http\Requests\TeacherAttendance\StoreTeacherAttendanceRequest;
use App\Http\Requests\TeacherAttendance\UpdateTeacherAttendanceRequest;
use App\Models\TeacherAttendance;
use App\Services\TeacherAttendance\CreateTeacherAttendanceService;
use App\Services\TeacherAttendance\UpdateTeacherAttendanceService;
use Illuminate\Http\JsonResponse;

class TeacherAttendanceController extends Controller
{
    protected $createTeacherAttendanceService;
    protected $updateTeacherAttendanceService;

    public function __construct(
        CreateTeacherAttendanceService $createTeacherAttendanceService,
        UpdateTeacherAttendanceService $updateTeacherAttendanceService
    ) {
        $this->createTeacherAttendanceService = $createTeacherAttendanceService;
        $this->updateTeacherAttendanceService = $updateTeacherAttendanceService;
    }

    public function index(): JsonResponse
    {
        $attendances = TeacherAttendance::with(['teacher', 'classSchedule', 'geofence', 'originalTeacher'])->paginate(10);
        return response()->json([
            'message' => 'Teacher attendances retrieved successfully',
            'data' => $attendances,
        ], 200);
    }

    public function store(StoreTeacherAttendanceRequest $request): JsonResponse
    {
        try {
            $attendance = $this->createTeacherAttendanceService->execute($request->validated());
            return response()->json([
                'message' => 'Teacher attendance created successfully',
                'data' => $attendance->load(['teacher', 'classSchedule', 'geofence', 'originalTeacher']),
            ], 201);
        } catch (\Exception $e) {
            return response()->json([
                'message' => 'Failed to create teacher attendance',
                'error' => $e->getMessage(),
            ], 500);
        }
    }

    public function show(TeacherAttendance $teacherAttendance): JsonResponse
    {
        return response()->json([
            'message' => 'Teacher attendance retrieved successfully',
            'data' => $teacherAttendance->load(['teacher', 'classSchedule', 'geofence', 'originalTeacher']),
        ], 200);
    }

    public function update(UpdateTeacherAttendanceRequest $request, TeacherAttendance $teacherAttendance): JsonResponse
    {
        try {
            $attendance = $this->updateTeacherAttendanceService->execute($teacherAttendance, $request->validated());
            return response()->json([
                'message' => 'Teacher attendance updated successfully',
                'data' => $attendance->load(['teacher', 'classSchedule', 'geofence', 'originalTeacher']),
            ], 200);
        } catch (\Exception $e) {
            return response()->json([
                'message' => 'Failed to update teacher attendance',
                'error' => $e->getMessage(),
            ], 500);
        }
    }

    public function destroy(TeacherAttendance $teacherAttendance): JsonResponse
    {
        try {
            $teacherAttendance->delete();
            return response()->json([
                'message' => 'Teacher attendance deleted successfully',
            ], 200);
        } catch (\Exception $e) {
            return response()->json([
                'message' => 'Failed to delete teacher attendance',
                'error' => $e->getMessage(),
            ], 500);
        }
    }
}
```

---

## Request Classes

### 1. `StoreUserRequest.php`
```php
<?php

namespace App\Http\Requests\User;

use Illuminate\Foundation\Http\FormRequest;

class StoreUserRequest extends FormRequest
{
    public function authorize(): bool
    {
        return true; // Adjust based on authorization logic
    }

    public function rules(): array
    {
        return [
            'username' => ['required', 'string', 'max:255', 'unique:users'],
            'name' => ['required', 'string', 'max:255'],
            'email' => ['required', 'email', 'max:255', 'unique:users'],
            'password' => ['required', 'string', 'min:8', 'confirmed'],
            'phone_number' => ['nullable', 'string', 'max:20'],
            'status' => ['required', 'boolean'],
            'photo_path' => ['nullable', 'string', 'max:255'],
        ];
    }
}
```

### 2. `UpdateUserRequest.php`
```php
<?php

namespace App\Http\Requests\User;

use Illuminate\Foundation\Http\FormRequest;

class UpdateUserRequest extends FormRequest
{
    public function authorize(): bool
    {
        return true; // Adjust based on authorization logic
    }

    public function rules(): array
    {
        return [
            'username' => ['sometimes', 'string', 'max:255', 'unique:users,username,' . $this->user->id],
            'name' => ['sometimes', 'string', 'max:255'],
            'email' => ['sometimes', 'email', 'max:255', 'unique:users,email,' . $this->user->id],
            'password' => ['sometimes', 'string', 'min:8', 'confirmed'],
            'phone_number' => ['nullable', 'string', 'max:20'],
            'status' => ['sometimes', 'boolean'],
            'photo_path' => ['nullable', 'string', 'max:255'],
        ];
    }
}
```

### 3. `StoreTeacherRequest.php`
```php
<?php

namespace App\Http\Requests\Teacher;

use Illuminate\Foundation\Http\FormRequest;

class StoreTeacherRequest extends FormRequest
{
    public function authorize(): bool
    {
        return true; // Adjust based on authorization logic
    }

    public function rules(): array
    {
        return [
            'user_id' => ['required', 'exists:users,id'],
            'nip' => ['nullable', 'string', 'max:255', 'unique:teachers'],
            'birth_place' => ['required', 'string', 'max:255'],
            'birth_date' => ['required', 'date'],
            'gender' => ['required', 'in:male,female'],
            'phone_number' => ['required', 'string', 'max:20'],
            'full_address' => ['required', 'string'],
            'gps_consent' => ['required', 'boolean'],
            'gps_consent_given_at' => ['nullable', 'date'],
        ];
    }
}
```

### 4. `UpdateTeacherRequest.php`
```php
<?php

namespace App\Http\Requests\Teacher;

use Illuminate\Foundation\Http\FormRequest;

class UpdateTeacherRequest extends FormRequest
{
    public function authorize(): bool
    {
        return true; // Adjust based on authorization logic
    }

    public function rules(): array
    {
        return [
            'nip' => ['sometimes', 'string', 'max:255', 'unique:teachers,nip,' . $this->teacher->id],
            'birth_place' => ['sometimes', 'string', 'max:255'],
            'birth_date' => ['sometimes', 'date'],
            'gender' => ['sometimes', 'in:male,female'],
            'phone_number' => ['sometimes', 'string', 'max:20'],
            'full_address' => ['sometimes', 'string'],
            'gps_consent' => ['sometimes', 'boolean'],
            'gps_consent_given_at' => ['nullable', 'date'],
        ];
    }
}
```

### 5. `StoreStudentRequest.php`
```php
<?php

namespace App\Http\Requests\Student;

use Illuminate\Foundation\Http\FormRequest;

class StoreStudentRequest extends FormRequest
{
    public function authorize(): bool
    {
        return true; // Adjust based on authorization logic
    }

    public function rules(): array
    {
        return [
            'user_id' => ['required', 'exists:users,id'],
            'nis' => ['required', 'string', 'max:20', 'unique:students'],
            'nisn' => ['required', 'string', 'max:20', 'unique:students'],
            'birth_place' => ['required', 'string', 'max:255'],
            'birth_date' => ['required', 'date'],
            'gender' => ['required', 'in:male,female'],
            'religion' => ['required', 'string', 'max:20'],
            'address' => ['required', 'string'],
            'entry_year' => ['required', 'integer', 'min:1900', 'max:9999'],
            'profile_picture' => ['nullable', 'string', 'max:255'],
            'phone' => ['nullable', 'string', 'max:20'],
        ];
    }
}
```

### 6. `UpdateStudentRequest.php`
```php
<?php

namespace App\Http\Requests\Student;

use Illuminate\Foundation\Http\FormRequest;

class UpdateStudentRequest extends FormRequest
{
    public function authorize(): bool
    {
        return true; // Adjust based on authorization logic
    }

    public function rules(): array
    {
        return [
            'nis' => ['sometimes', 'string', 'max:20', 'unique:students,nis,' . $this->student->id],
            'nisn' => ['sometimes', 'string', 'max:20', 'unique:students,nisn,' . $this->student->id],
            'birth_place' => ['sometimes', 'string', 'max:255'],
            'birth_date' => ['sometimes', 'date'],
            'gender' => ['sometimes', 'in:male,female'],
            'religion' => ['sometimes', 'string', 'max:20'],
            'address' => ['sometimes', 'string'],
            'entry_year' => ['sometimes', 'integer', 'min:1900', 'max:9999'],
            'profile_picture' => ['nullable', 'string', 'max:255'],
            'phone' => ['nullable', 'string', 'max:20'],
        ];
    }
}
```

### 7. `StoreClassroomRequest.php`
```php
<?php

namespace App\Http\Requests\Classroom;

use Illuminate\Foundation\Http\FormRequest;

class StoreClassroomRequest extends FormRequest
{
    public function authorize(): bool
    {
        return true; // Adjust based on authorization logic
    }

    public function rules(): array
    {
        return [
            'name' => ['required', 'string', 'max:255'],
            'level' => ['required', 'in:7,8,9'],
            'capacity' => ['required', 'integer', 'min:1'],
            'program_id' => ['required', 'exists:programs,id'],
            'shift_id' => ['nullable', 'exists:shifts,id'],
            'teacher_id' => ['nullable', 'exists:teachers,id'],
            'academic_year_id' => ['required', 'exists:academic_years,id'],
            'status' => ['required', 'in:active,inactive'],
            'description' => ['nullable', 'string'],
        ];
    }
}
```

### 8. `UpdateClassroomRequest.php`
```php
<?php

namespace App\Http\Requests\Classroom;

use Illuminate\Foundation\Http\FormRequest;

class UpdateClassroomRequest extends FormRequest
{
    public function authorize(): bool
    {
        return true; // Adjust based on authorization logic
    }

    public function rules(): array
    {
        return [
            'name' => ['sometimes', 'string', 'max:255'],
            'level' => ['sometimes', 'in:7,8,9'],
            'capacity' => ['sometimes', 'integer', 'min:1'],
            'program_id' => ['sometimes', 'exists:programs,id'],
            'shift_id' => ['nullable', 'exists:shifts,id'],
            'teacher_id' => ['nullable', 'exists:teachers,id'],
            'academic_year_id' => ['sometimes', 'exists:academic_years,id'],
            'status' => ['sometimes', 'in:active,inactive'],
            'description' => ['nullable', 'string'],
        ];
    }
}
```

### 9. `StoreTeacherAttendanceRequest.php`
```php
<?php

namespace App\Http\Requests\TeacherAttendance;

use Illuminate\Foundation\Http\FormRequest;

class StoreTeacherAttendanceRequest extends FormRequest
{
    public function authorize(): bool
    {
        return true; // Adjust based on authorization logic
    }

    public function rules(): array
    {
        return [
            'teacher_id' => ['required', 'exists:teachers,id'],
            'class_schedule_id' => ['required', 'exists:class_schedules,id'],
            'attendance_date' => ['required', 'date'],
            'check_in_time' => ['required', 'date_format:Y-m-d H:i:s'],
            'check_out_time' => ['nullable', 'date_format:Y-m-d H:i:s', 'after:check_in_time'],
            'latitude' => ['required', 'numeric', 'between:-90,90'],
            'longitude' => ['required', 'numeric', 'between:-180,180'],
            'geofence_id' => ['nullable', 'exists:geofences,id'],
            'status' => ['required', 'in:present,absent,late,substitute'],
            'is_substitute_teacher' => ['required', 'boolean'],
            'original_teacher_id' => ['nullable', 'exists:teachers,id'],
            'notes' => ['nullable', 'string'],
        ];
    }
}
```

### 10. `UpdateTeacherAttendanceRequest.php`
```php
<?php

namespace App\Http\Requests\TeacherAttendance;

use Illuminate\Foundation\Http\FormRequest;

class UpdateTeacherAttendanceRequest extends FormRequest
{
    public function authorize(): bool
    {
        return true; // Adjust based on authorization logic
    }

    public function rules(): array
    {
        return [
            'teacher_id' => ['sometimes', 'exists:teachers,id'],
            'class_schedule_id' => ['sometimes', 'exists:class_schedules,id'],
            'attendance_date' => ['sometimes', 'date'],
            'check_in_time' => ['sometimes', 'date_format:Y-m-d H:i:s'],
            'check_out_time' => ['nullable', 'date_format:Y-m-d H:i:s', 'after:check_in_time'],
            'latitude' => ['sometimes', 'numeric', 'between:-90,90'],
            'longitude' => ['sometimes', 'numeric', 'between:-180,180'],
            'geofence_id' => ['nullable', 'exists:geofences,id'],
            'status' => ['sometimes', 'in:present,absent,late,substitute'],
            'is_substitute_teacher' => ['sometimes', 'boolean'],
            'original_teacher_id' => ['nullable', 'exists:teachers,id'],
            'notes' => ['nullable', 'string'],
        ];
    }
}
```

---

## Services

### 1. `CreateUserService.php`
```php
<?php

namespace App\Services\User;

use App\Models\User;
use Illuminate\Support\Facades\Hash;

class CreateUserService
{
    public function execute(array $data): User
    {
        $data['password'] = Hash::make($data['password']);
        return User::create($data);
    }
}
```

### 2. `UpdateUserService.php`
```php
<?php

namespace App\Services\User;

use App\Models\User;
use Illuminate\Support\Facades\Hash;

class UpdateUserService
{
    public function execute(User $user, array $data): User
    {
        if (isset($data['password'])) {
            $data['password'] = Hash::make($data['password']);
        }
        $user->update($data);
        return $user;
    }
}
```

### 3. `CreateTeacherService.php`
```php
<?php

namespace App\Services\Teacher;

use App\Models\Teacher;
use App\Models\User;
use Illuminate\Support\Facades\DB;

class CreateTeacherService
{
    public function execute(array $data): Teacher
    {
        return DB::transaction(function () use ($data) {
            $user = User::findOrFail($data['user_id']);
            if ($user->teacher) {
                throw new \Exception('User is already associated with a teacher.');
            }
            return Teacher::create($data);
        });
    }
}
```

### 4. `UpdateTeacherService.php`
```php
<?php

namespace App\Services\Teacher;

use App\Models\Teacher;

class UpdateTeacherService
{
    public function execute(Teacher $teacher, array $data): Teacher
    {
        $teacher->update($data);
        return $teacher;
    }
}
```

### 5. `CreateStudentService.php`
```php
<?php

namespace App\Services\Student;

use App\Models\Student;
use App\Models\User;
use Illuminate\Support\Facades\DB;

class CreateStudentService
{
    public function execute(array $data): Student
    {
        return DB::transaction(function () use ($data) {
            $user = User::findOrFail($data['user_id']);
            if ($user->student) {
                throw new \Exception('User is already associated with a student.');
            }
            return Student::create($data);
        });
    }
}
```

### 6. `UpdateStudentService.php`
```php
<?php

namespace App\Services\Student;

use App\Models\Student;

class UpdateStudentService
{
    public function execute(Student $student, array $data): Student
    {
        $student->update($data);
        return $student;
    }
}
```

### 7. `CreateClassroomService.php`
```php
<?php

namespace App\Services\Classroom;

use App\Models\Classroom;

class CreateClassroomService
{
    public function execute(array $data): Classroom
    {
        return Classroom::create($data);
    }
}
```

### 8. `UpdateClassroomService.php`
```php
<?php

namespace App\Services\Classroom;

use App\Models\Classroom;

class UpdateClassroomService
{
    public function execute(Classroom $classroom, array $data): Classroom
    {
        $classroom->update($data);
        return $classroom;
    }
}
```

### 9. `CreateTeacherAttendanceService.php`
```php
<?php

namespace App\Services\TeacherAttendance;

use App\Models\Geofence;
use App\Models\TeacherAttendance;
use Illuminate\Support\Facades\DB;

class CreateTeacherAttendanceService
{
    public function execute(array $data): TeacherAttendance
    {
        return DB::transaction(function () use ($data) {
            if (isset($data['geofence_id'])) {
                $geofence = Geofence::findOrFail($data['geofence_id']);
                $distance = $this->calculateDistance(
                    $data['latitude'],
                    $data['longitude'],
                    $geofence->latitude,
                    $geofence->longitude
                );
                if ($distance > $geofence->radius) {
                    throw new \Exception('Teacher is outside the geofence area.');
                }
            }
            return TeacherAttendance::create($data);
        });
    }

    private function calculateDistance(float $lat1, float $lon1, float $lat2, float $lon2): float
    {
        // Haversine formula to calculate distance in meters
        $earthRadius = 6371000; // meters
        $dLat = deg2rad($lat2 - $lat1);
        $dLon = deg2rad($lon2 - $lon1);
        $a = sin($dLat / 2) * sin($dLat / 2) +
             cos(deg2rad($lat1)) * cos(deg2rad($lat2)) *
             sin($dLon / 2) *-money($dLon / 2);
        $c = 2 * atan2(sqrt($a), sqrt(1 - $a));
        return $earthRadius * $c;
    }
}
```

### 10. `UpdateTeacherAttendanceService.php`
```php
<?php

namespace App\Services\TeacherAttendance;

use App\Models\Geofence;
use App\Models\TeacherAttendance;
use Illuminate\Support\Facades\DB;

class UpdateTeacherAttendanceService
{
    public function execute(TeacherAttendance $attendance, array $data): TeacherAttendance
    {
        return DB::transaction(function () use ($attendance, $data) {
            if (isset($data['geofence_id']) && isset($data['latitude']) && isset($data['longitude'])) {
                $geofence = Geofence::findOrFail($data['geofence_id']);
                $distance = $this->calculateDistance(
                    $data['latitude'],
                    $data['longitude'],
                    $geofence->latitude,
                    $geofence->longitude
                );
                if ($distance > $geofence->radius) {
                    throw new \Exception('Teacher is outside the geofence area.');
                }
            }
            $attendance->update($data);
            return $attendance;
        });
    }

    private function calculateDistance(float $lat1, float $lon1, float $lat2, float $lon2): float
    {
        // Haversine formula to calculate distance in meters
        $earthRadius = 6371000; // meters
        $dLat = deg2rad($lat2 - $lat1);
        $dLon = deg2rad($lon2 - $lon1);
        $a = sin($dLat / 2) * sin($dLat / 2) +
             cos(deg2rad($lat1)) * cos(deg2rad($lat2)) *
             sin($dLon / 2) * sin($dLon / 2);
        $c = 2 * atan2(sqrt($a), sqrt(1 - $a));
        return $earthRadius * $c;
    }
}
```

---

## Notes
- **Best Practices**:
  - **Dependency Injection**: Services are injected into controllers for better testability and separation of concerns.
  - **Request Validation**: Custom request classes (`Store*Request`, `Update*Request`) handle validation to keep controllers clean.
  - **Error Handling**: Try-catch blocks in controllers handle exceptions gracefully, returning JSON responses with appropriate status codes.
  - **Transactions**: Complex operations (e.g., creating teachers/students with user associations, geofence validation) use database transactions to ensure data integrity.
  - **RESTful Responses**: Consistent JSON response structure (`message`, `data`, `error`) for all endpoints.
  - **Naming Conventions**: Services are named clearly (e.g., `CreateTeacherService`, `UpdateTeacherService`) to reflect their specific actions.
- **Complex Logic in Services**:
  - `CreateTeacherService` and `CreateStudentService` check for existing associations with users to prevent duplicates.
  - `CreateTeacherAttendanceService` and `UpdateTeacherAttendanceService` include geofence validation using the Haversine formula to ensure the teacher is within the geofence radius.
- **Authorization**: The `authorize` method in request classes is set to `true` for simplicity. Implement proper authorization (e.g., using Laravel policies or Spatie permissions) based on your requirements.
- **Scalability**: The pattern can be extended to other entities (e.g., `Parents`, `Geofences`, `PayrollRecords`) by creating similar controllers, services, and request classes.
- **File Organization**:
  - Place controllers in `app/Http/Controllers`.
  - Place services in `app/Services`.
  - Place request classes in `app/Http/Requests`.
- **Service Provider**: Register services in a service provider (e.g., `AppServiceProvider`) for dependency injection:
  ```php
  public function register()
  {
      $this->app->bind(CreateUserService::class, fn () => new CreateUserService());
      $this->app->bind(UpdateUserService::class, fn () => new UpdateUserService());
      // Repeat for other services
  }
  ```
- **Routes**: Define API routes in `routes/api.php`:
  ```php
  Route::apiResource('users', UserController::class);
  Route::apiResource('teachers', TeacherController::class);
  Route::apiResource('students', StudentController::class);
  Route::apiResource('classrooms', ClassroomController::class);
  Route::apiResource('teacher-attendances', TeacherAttendanceController::class);
  ```

This implementation provides a solid, consistent, and maintainable CRUD system for the `db_rawooh_v2` database, with services handling complex logic and controllers managing HTTP interactions.