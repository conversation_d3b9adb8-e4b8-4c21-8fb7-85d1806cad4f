<?php

namespace App\Http\Controllers;

use App\Http\Requests\Program\StoreProgramRequest;
use App\Http\Requests\Program\UpdateProgramRequest;
use App\Models\Program;
use Illuminate\Http\RedirectResponse;
use Illuminate\Http\Request;
use Illuminate\View\View;

class ProgramController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index(Request $request): View
    {
        $query = Program::with(['students']);

        // Apply search if provided
        if ($request->filled('search')) {
            $search = $request->get('search');
            $query->where(function ($q) use ($search) {
                $q->where('name', 'like', "%{$search}%")
                  ->orWhere('code', 'like', "%{$search}%")
                  ->orWhere('description', 'like', "%{$search}%");
            });
        }

        // Apply status filter if provided
        if ($request->filled('status')) {
            $query->where('status', $request->get('status'));
        }

        // Apply education level filter if provided
        if ($request->filled('education_level')) {
            $query->where('education_level', $request->get('education_level'));
        }

        // Apply sorting
        $sortBy = $request->get('sort_by', 'created_at');
        $sortDirection = $request->get('sort_direction', 'desc');
        $query->orderBy($sortBy, $sortDirection);

        $programs = $query->paginate(15);
        $programs->appends($request->query());

        return view('programs.index', compact('programs'));
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create(): View
    {
        return view('programs.create');
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(StoreProgramRequest $request): RedirectResponse
    {
        try {
            $data = $request->validated();
            
            // Set default status if not provided
            if (!isset($data['status'])) {
                $data['status'] = 'active';
            }

            $program = Program::create($data);

            // Log program creation
            logger()->info('Program created', [
                'program_id' => $program->id,
                'code' => $program->code,
                'name' => $program->name,
                'created_by' => auth()->id(),
            ]);

            return redirect()
                ->route('programs.show', $program)
                ->with('success', 'Program created successfully.');
        } catch (\Exception $e) {
            return back()
                ->withInput()
                ->with('error', 'Failed to create program: ' . $e->getMessage());
        }
    }

    /**
     * Display the specified resource.
     */
    public function show(Program $program): View
    {
        $program->load(['students']);
        
        return view('programs.show', compact('program'));
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(Program $program): View
    {
        return view('programs.edit', compact('program'));
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(UpdateProgramRequest $request, Program $program): RedirectResponse
    {
        try {
            $data = $request->validated();
            
            $program->update($data);

            // Log program update
            logger()->info('Program updated', [
                'program_id' => $program->id,
                'code' => $program->code,
                'name' => $program->name,
                'updated_by' => auth()->id(),
            ]);

            return redirect()
                ->route('programs.show', $program)
                ->with('success', 'Program updated successfully.');
        } catch (\Exception $e) {
            return back()
                ->withInput()
                ->with('error', 'Failed to update program: ' . $e->getMessage());
        }
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(Program $program): RedirectResponse
    {
        try {
            // Check if program has active students
            if ($program->students()->exists()) {
                return back()->with('error', 'Cannot delete program with enrolled students.');
            }

            // Log program deletion before deleting
            logger()->info('Program deleted', [
                'program_id' => $program->id,
                'code' => $program->code,
                'name' => $program->name,
                'deleted_by' => auth()->id(),
            ]);

            $program->delete();

            return redirect()
                ->route('programs.index')
                ->with('success', 'Program deleted successfully.');
        } catch (\Exception $e) {
            return back()
                ->with('error', 'Failed to delete program: ' . $e->getMessage());
        }
    }
}
