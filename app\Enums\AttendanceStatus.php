<?php

namespace App\Enums;

enum AttendanceStatus: string
{
    case PRESENT = 'present';
    case ABSENT = 'absent';
    case LATE = 'late';
    case EARLY_LEAVE = 'early_leave';
    case SICK = 'sick';
    case PERMISSION = 'permission';
    case OFFICIAL_DUTY = 'official_duty';

    public function label(): string
    {
        return match($this) {
            self::PRESENT => 'Hadir',
            self::ABSENT => 'Tidak Hadir',
            self::LATE => 'Terlambat',
            self::EARLY_LEAVE => 'Pulang Awal',
            self::SICK => 'Sakit',
            self::PERMISSION => 'Izin',
            self::OFFICIAL_DUTY => 'Tugas Dinas',
        };
    }

    public static function options(): array
    {
        return [
            self::PRESENT->value => self::PRESENT->label(),
            self::ABSENT->value => self::ABSENT->label(),
            self::LATE->value => self::LATE->label(),
            self::EARLY_LEAVE->value => self::EARLY_LEAVE->label(),
            self::SICK->value => self::SICK->label(),
            self::PERMISSION->value => self::PERMISSION->label(),
            self::OFFICIAL_DUTY->value => self::OFFICIAL_DUTY->label(),
        ];
    }
}
