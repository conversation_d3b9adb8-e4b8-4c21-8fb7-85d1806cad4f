<?php

namespace App\Http\Requests\Teacher;

use Illuminate\Foundation\Http\FormRequest;

class StoreTeacherRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true; // Adjust based on authorization logic
    }

    /**
     * Get the validation rules that apply to the request.
     */
    public function rules(): array
    {
        return [
            'user_id' => [
                'required',
                'exists:users,id',
                'unique:teachers,user_id',
            ],
            'employee_id' => [
                'required',
                'string',
                'max:50',
                'unique:teachers,employee_id',
            ],
            'nip' => [
                'nullable',
                'string',
                'max:50',
                'unique:teachers,nip',
            ],
            'full_name' => [
                'required',
                'string',
                'max:255',
            ],
            'gender' => [
                'required',
                'in:male,female',
            ],
            'birth_date' => [
                'nullable',
                'date',
                'before:today',
            ],
            'birth_place' => [
                'nullable',
                'string',
                'max:100',
            ],
            'religion' => [
                'nullable',
                'in:islam,kristen,katolik,hindu,buddha,konghucu',
            ],
            'blood_type' => [
                'nullable',
                'in:A,B,AB,O',
            ],
            'education_level' => [
                'nullable',
                'in:SD,SMP,SMA,D3,S1,S2,S3',
            ],
            'education_major' => [
                'nullable',
                'string',
                'max:100',
            ],
            'employment_status' => [
                'required',
                'in:permanent,contract,temporary,probation',
            ],
            'hire_date' => [
                'required',
                'date',
                'before_or_equal:today',
            ],
            'contract_end_date' => [
                'nullable',
                'date',
                'after:hire_date',
            ],
            'basic_salary' => [
                'nullable',
                'numeric',
                'min:0',
            ],
            'status' => [
                'required',
                'in:active,inactive,suspended,terminated',
            ],
            'notes' => [
                'nullable',
                'string',
            ],
        ];
    }

    /**
     * Get custom messages for validator errors.
     */
    public function messages(): array
    {
        return [
            'user_id.required' => 'User is required.',
            'user_id.exists' => 'Selected user does not exist.',
            'user_id.unique' => 'This user is already assigned as a teacher.',
            'employee_id.required' => 'Employee ID is required.',
            'employee_id.unique' => 'This employee ID is already taken.',
            'nip.unique' => 'This NIP is already taken.',
            'full_name.required' => 'Full name is required.',
            'gender.required' => 'Gender is required.',
            'gender.in' => 'Gender must be either male or female.',
            'birth_date.date' => 'Please provide a valid birth date.',
            'birth_date.before' => 'Birth date must be before today.',
            'religion.in' => 'Please select a valid religion.',
            'blood_type.in' => 'Please select a valid blood type.',
            'education_level.in' => 'Please select a valid education level.',
            'employment_status.required' => 'Employment status is required.',
            'employment_status.in' => 'Please select a valid employment status.',
            'hire_date.required' => 'Hire date is required.',
            'hire_date.date' => 'Please provide a valid hire date.',
            'hire_date.before_or_equal' => 'Hire date cannot be in the future.',
            'contract_end_date.after' => 'Contract end date must be after hire date.',
            'basic_salary.numeric' => 'Basic salary must be a number.',
            'basic_salary.min' => 'Basic salary cannot be negative.',
            'status.required' => 'Status is required.',
            'status.in' => 'Please select a valid status.',
        ];
    }

    /**
     * Get custom attributes for validator errors.
     */
    public function attributes(): array
    {
        return [
            'user_id' => 'user',
            'employee_id' => 'employee ID',
            'nip' => 'NIP',
            'full_name' => 'full name',
            'birth_date' => 'birth date',
            'birth_place' => 'birth place',
            'education_level' => 'education level',
            'education_major' => 'education major',
            'employment_status' => 'employment status',
            'hire_date' => 'hire date',
            'contract_end_date' => 'contract end date',
            'basic_salary' => 'basic salary',
        ];
    }
}
