<?php

namespace App\Models;

use App\Enums\GeneralStatus;
use Illuminate\Database\Eloquent\Model;

class NotificationTemplate extends Model
{
    protected $fillable = [
        'name',
        'type',
        'subject',
        'body_template',
        'variables',
        'status'
    ];

    protected $casts = [
        'variables' => 'json',
        'status' => GeneralStatus::class,
    ];

    public function scopeActive($query)
    {
        return $query->where('status', GeneralStatus::Active);
    }

    public function scopeByType($query, $type)
    {
        return $query->where('type', $type);
    }

    public function renderBody(array $data = []): string
    {
        $body = $this->body_template;

        foreach ($data as $key => $value) {
            $body = str_replace("{{$key}}", $value, $body);
        }

        return $body;
    }

    public function renderSubject(array $data = []): string
    {
        $subject = $this->subject;

        foreach ($data as $key => $value) {
            $subject = str_replace("{{$key}}", $value, $subject);
        }

        return $subject;
    }

    public function getAvailableVariables(): array
    {
        return $this->variables ?? [];
    }
}
