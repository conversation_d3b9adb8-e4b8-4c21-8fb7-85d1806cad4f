<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('geofence_logs', function (Blueprint $table) {
            $table->id();
            $table->foreignId('geofence_id')->constrained()->onDelete('cascade');
            $table->enum('user_type', ['teacher', 'student']);
            $table->unsignedBigInteger('user_id');
            $table->enum('event_type', ['enter', 'exit']);
            $table->decimal('latitude', 10, 8);
            $table->decimal('longitude', 11, 8);
            $table->timestamp('event_time');
            $table->string('device_info', 200)->nullable();
            $table->timestamps();

            // Indexes
            $table->index('geofence_id');
            $table->index(['user_type', 'user_id']);
            $table->index('event_type');
            $table->index('event_time');
            $table->index(['latitude', 'longitude']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('geofence_logs');
    }
};
