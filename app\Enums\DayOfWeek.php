<?php

namespace App\Enums;

enum DayOfWeek: string
{
    case MONDAY = 'monday';
    case TUESDAY = 'tuesday';
    case WEDNESDAY = 'wednesday';
    case THURSDAY = 'thursday';
    case FRIDAY = 'friday';
    case SATURDAY = 'saturday';
    case SUNDAY = 'sunday';

    public function label(): string
    {
        return match($this) {
            self::MONDAY => 'Senin',
            self::TUESDAY => 'Selasa',
            self::WEDNESDAY => 'Rabu',
            self::THURSDAY => 'Kamis',
            self::FRIDAY => 'Jumat',
            self::SATURDAY => 'Sabtu',
            self::SUNDAY => 'Minggu',
        };
    }

    public static function options(): array
    {
        return [
            self::MONDAY->value => self::MONDAY->label(),
            self::TUESDAY->value => self::TUESDAY->label(),
            self::WEDNESDAY->value => self::WEDNESDAY->label(),
            self::THURSDAY->value => self::THURSDAY->label(),
            self::FRIDAY->value => self::FRIDAY->label(),
            self::SATURDAY->value => self::SATURDAY->label(),
            self::SUNDAY->value => self::SUNDAY->label(),
        ];
    }
}
