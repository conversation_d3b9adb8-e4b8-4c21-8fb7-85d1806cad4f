<?php

namespace App\Http\Controllers;

use App\Http\Requests\Shift\StoreShiftRequest;
use App\Http\Requests\Shift\UpdateShiftRequest;
use App\Models\Shift;
use Illuminate\Http\RedirectResponse;
use Illuminate\Http\Request;
use Illuminate\View\View;

class ShiftController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index(Request $request): View
    {
        $query = Shift::with(['teachers']);

        // Apply search if provided
        if ($request->filled('search')) {
            $search = $request->get('search');
            $query->where(function ($q) use ($search) {
                $q->where('name', 'like', "%{$search}%")
                    ->orWhere('description', 'like', "%{$search}%");
            });
        }

        // Apply status filter if provided
        if ($request->filled('status')) {
            $query->where('status', $request->get('status'));
        }

        // Apply sorting
        $sortBy = $request->get('sort_by', 'start_time');
        $sortDirection = $request->get('sort_direction', 'asc');
        $query->orderBy($sortBy, $sortDirection);

        $shifts = $query->paginate(15);
        $shifts->appends($request->query());

        return view('shifts.index', compact('shifts'));
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create(): View
    {
        return view('shifts.create');
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(StoreShiftRequest $request): RedirectResponse
    {
        try {
            $data = $request->validated();

            // Set default status if not provided
            if (!isset($data['status'])) {
                $data['status'] = 'active';
            }

            $shift = Shift::create($data);

            // Log shift creation
            logger()->info('Shift created', [
                'shift_id' => $shift->id,
                'name' => $shift->name,
                'start_time' => $shift->start_time,
                'end_time' => $shift->end_time,
                'created_by' => auth()->id(),
            ]);

            return redirect()
                ->route('shifts.show', $shift)
                ->with('success', 'Shift created successfully.');
        } catch (\Exception $e) {
            return back()
                ->withInput()
                ->with('error', 'Failed to create shift: ' . $e->getMessage());
        }
    }

    /**
     * Display the specified resource.
     */
    public function show(Shift $shift): View
    {
        $shift->load(['teachers']);

        return view('shifts.show', compact('shift'));
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(Shift $shift): View
    {
        return view('shifts.edit', compact('shift'));
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(UpdateShiftRequest $request, Shift $shift): RedirectResponse
    {
        try {
            $data = $request->validated();

            $shift->update($data);

            // Log shift update
            logger()->info('Shift updated', [
                'shift_id' => $shift->id,
                'name' => $shift->name,
                'start_time' => $shift->start_time,
                'end_time' => $shift->end_time,
                'updated_by' => auth()->id(),
            ]);

            return redirect()
                ->route('shifts.show', $shift)
                ->with('success', 'Shift updated successfully.');
        } catch (\Exception $e) {
            return back()
                ->withInput()
                ->with('error', 'Failed to update shift: ' . $e->getMessage());
        }
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(Shift $shift): RedirectResponse
    {
        try {
            // Check if shift has assigned teachers
            if ($shift->teachers()->exists()) {
                return back()->with('error', 'Cannot delete shift with assigned teachers.');
            }

            // Log shift deletion before deleting
            logger()->info('Shift deleted', [
                'shift_id' => $shift->id,
                'name' => $shift->name,
                'deleted_by' => auth()->id(),
            ]);

            $shift->delete();

            return redirect()
                ->route('shifts.index')
                ->with('success', 'Shift deleted successfully.');
        } catch (\Exception $e) {
            return back()
                ->with('error', 'Failed to delete shift: ' . $e->getMessage());
        }
    }
}
