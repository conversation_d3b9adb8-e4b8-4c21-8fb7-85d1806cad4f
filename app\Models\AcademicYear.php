<?php

namespace App\Models;

use App\Enums\AcademicYearStatus;
use App\Enums\Semester;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;

class AcademicYear extends Model
{
    protected $fillable = [
        'year',
        'semester',
        'start_date',
        'end_date',
        'registration_start',
        'registration_end',
        'status',
        'is_current'
    ];

    protected $casts = [
        'semester' => Semester::class,
        'status' => AcademicYearStatus::class,
        'start_date' => 'date',
        'end_date' => 'date',
        'registration_start' => 'date',
        'registration_end' => 'date',
        'is_current' => 'boolean',
    ];

    public function classrooms(): HasMany
    {
        return $this->hasMany(Classroom::class);
    }

    public function scopeCurrent($query)
    {
        return $query->where('is_current', true);
    }

    public function scopeActive($query)
    {
        return $query->where('status', AcademicYearStatus::Active);
    }
}
