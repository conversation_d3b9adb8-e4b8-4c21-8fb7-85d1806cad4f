<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

return new class extends Migration {
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('academic_years', function (Blueprint $table) {
            $table->id();
            $table->string('name', 100);
            $table->string('code', 20)->unique();
            $table->enum('semester', ['odd', 'even']);
            $table->date('start_date');
            $table->date('end_date');
            $table->date('registration_start')->nullable();
            $table->date('registration_end')->nullable();
            $table->enum('status', ['planned', 'active', 'completed', 'cancelled'])->default('planned');
            $table->text('description')->nullable();
            $table->timestamps();

            // Indexes
            $table->index('status');
            $table->index(['start_date', 'end_date']);
            $table->index('code');
        });

        // Add check constraints using raw SQL
        DB::statement('ALTER TABLE academic_years ADD CONSTRAINT chk_academic_year_dates CHECK (end_date > start_date)');
        DB::statement('ALTER TABLE academic_years ADD CONSTRAINT chk_registration_dates CHECK (registration_end >= registration_start OR registration_end IS NULL OR registration_start IS NULL)');
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('academic_years');
    }
};
