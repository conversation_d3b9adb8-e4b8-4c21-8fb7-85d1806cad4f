<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('schedule_exceptions', function (Blueprint $table) {
            $table->id();
            $table->foreignId('class_schedule_id')->constrained()->onDelete('cascade');
            $table->date('exception_date');
            $table->enum('exception_type', ['cancelled', 'rescheduled', 'substitute_teacher']);
            $table->foreignId('substitute_teacher_id')->nullable()->constrained('teachers')->onDelete('set null');
            $table->time('new_start_time')->nullable();
            $table->time('new_end_time')->nullable();
            $table->string('reason', 200);
            $table->text('notes')->nullable();
            $table->timestamps();

            // Indexes
            $table->index('class_schedule_id');
            $table->index('exception_date');
            $table->index('exception_type');
            $table->index('substitute_teacher_id');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('schedule_exceptions');
    }
};
