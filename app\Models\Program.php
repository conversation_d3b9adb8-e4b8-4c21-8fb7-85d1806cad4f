<?php

namespace App\Models;

use App\Enums\EducationLevel;
use App\Enums\GeneralStatus;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;

class Program extends Model
{
    protected $fillable = [
        'name',
        'code',
        'education_level',
        'duration_years',
        'description',
        'status'
    ];

    protected $casts = [
        'education_level' => EducationLevel::class,
        'status' => GeneralStatus::class,
        'duration_years' => 'integer',
    ];

    public function students(): HasMany
    {
        return $this->hasMany(Student::class);
    }

    public function scopeActive($query)
    {
        return $query->where('status', GeneralStatus::Active);
    }
}
