# Optimized Database Schema Part 2 - Classroom & Scheduling Tables

## Classroom Management Tables

### 12. Classrooms Table (Enhanced)
```sql
CREATE TABLE classrooms (
    id BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    program_id BIGINT UNSIGNED NOT NULL,
    shift_id BIGINT UNSIGNED NULL,
    academic_year_id BIGINT UNSIGNED NOT NULL,
    name VARCHAR(100) NOT NULL,
    code VARCHAR(50) UNIQUE NOT NULL,
    level ENUM('7', '8', '9') NOT NULL,
    capacity SMALLINT UNSIGNED NOT NULL,
    current_students SMALLINT UNSIGNED DEFAULT 0,
    homeroom_teacher_id BIGINT UNSIGNED NULL,
    room_number VARCHAR(50) NULL,
    building VARCHAR(100) NULL,
    floor TINYINT UNSIGNED NULL,
    facilities JSON NULL, -- ['projector', 'ac', 'whiteboard']
    status ENUM('active', 'inactive', 'maintenance') DEFAULT 'active',
    description TEXT NULL,
    created_at TIMESTAMP NULL,
    updated_at TIMESTAMP NULL,
    deleted_at TIMESTAMP NULL,
    
    INDEX idx_program (program_id),
    INDEX idx_shift (shift_id),
    INDEX idx_academic_year (academic_year_id),
    INDEX idx_homeroom_teacher (homeroom_teacher_id),
    INDEX idx_level (level),
    INDEX idx_status (status),
    INDEX idx_code (code),
    UNIQUE KEY unique_classroom_year (code, academic_year_id),
    CONSTRAINT chk_capacity CHECK (capacity > 0),
    CONSTRAINT chk_current_students CHECK (current_students <= capacity),
    FOREIGN KEY (program_id) REFERENCES programs(id) ON DELETE CASCADE,
    FOREIGN KEY (shift_id) REFERENCES shifts(id) ON DELETE SET NULL,
    FOREIGN KEY (academic_year_id) REFERENCES academic_years(id) ON DELETE CASCADE,
    FOREIGN KEY (homeroom_teacher_id) REFERENCES teachers(id) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
```

### 13. Classroom Students Table (Enhanced)
```sql
CREATE TABLE classroom_students (
    id BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    classroom_id BIGINT UNSIGNED NOT NULL,
    student_id BIGINT UNSIGNED NOT NULL,
    academic_year_id BIGINT UNSIGNED NOT NULL,
    enrollment_date DATE NOT NULL,
    seat_number SMALLINT UNSIGNED NULL,
    status ENUM('enrolled', 'transferred', 'dropped', 'graduated') DEFAULT 'enrolled',
    transfer_reason TEXT NULL,
    transfer_date DATE NULL,
    created_at TIMESTAMP NULL,
    updated_at TIMESTAMP NULL,
    
    INDEX idx_classroom (classroom_id),
    INDEX idx_student (student_id),
    INDEX idx_academic_year (academic_year_id),
    INDEX idx_status (status),
    INDEX idx_enrollment_date (enrollment_date),
    UNIQUE KEY unique_student_classroom_year (student_id, classroom_id, academic_year_id),
    FOREIGN KEY (classroom_id) REFERENCES classrooms(id) ON DELETE CASCADE,
    FOREIGN KEY (student_id) REFERENCES students(id) ON DELETE CASCADE,
    FOREIGN KEY (academic_year_id) REFERENCES academic_years(id) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
```

### 14. Teacher Assignments Table (Enhanced)
```sql
CREATE TABLE teacher_assignments (
    id BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    teacher_id BIGINT UNSIGNED NOT NULL,
    subject_id BIGINT UNSIGNED NOT NULL,
    classroom_id BIGINT UNSIGNED NOT NULL,
    academic_year_id BIGINT UNSIGNED NOT NULL,
    assignment_type ENUM('regular', 'substitute', 'temporary', 'co_teacher') DEFAULT 'regular',
    is_homeroom_teacher BOOLEAN DEFAULT FALSE,
    weekly_hours DECIMAL(3,1) DEFAULT 0.0,
    start_date DATE NULL,
    end_date DATE NULL,
    status ENUM('active', 'inactive', 'completed') DEFAULT 'active',
    notes TEXT NULL,
    created_at TIMESTAMP NULL,
    updated_at TIMESTAMP NULL,
    
    INDEX idx_teacher (teacher_id),
    INDEX idx_subject (subject_id),
    INDEX idx_classroom (classroom_id),
    INDEX idx_academic_year (academic_year_id),
    INDEX idx_assignment_type (assignment_type),
    INDEX idx_status (status),
    INDEX idx_homeroom (is_homeroom_teacher),
    UNIQUE KEY unique_teacher_subject_classroom_year (teacher_id, subject_id, classroom_id, academic_year_id),
    FOREIGN KEY (teacher_id) REFERENCES teachers(id) ON DELETE CASCADE,
    FOREIGN KEY (subject_id) REFERENCES subjects(id) ON DELETE CASCADE,
    FOREIGN KEY (classroom_id) REFERENCES classrooms(id) ON DELETE CASCADE,
    FOREIGN KEY (academic_year_id) REFERENCES academic_years(id) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
```

## Scheduling Tables

### 15. Class Schedules Table (Enhanced)
```sql
CREATE TABLE class_schedules (
    id BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    teacher_assignment_id BIGINT UNSIGNED NOT NULL,
    lesson_hour_id BIGINT UNSIGNED NOT NULL,
    academic_year_id BIGINT UNSIGNED NOT NULL,
    day_of_week ENUM('monday', 'tuesday', 'wednesday', 'thursday', 'friday', 'saturday', 'sunday') NOT NULL,
    effective_start_date DATE NOT NULL,
    effective_end_date DATE NULL,
    room_override VARCHAR(100) NULL, -- Override classroom location
    is_recurring BOOLEAN DEFAULT TRUE,
    recurrence_pattern ENUM('weekly', 'biweekly', 'monthly') DEFAULT 'weekly',
    status ENUM('active', 'inactive', 'cancelled') DEFAULT 'active',
    notes TEXT NULL,
    created_at TIMESTAMP NULL,
    updated_at TIMESTAMP NULL,
    
    INDEX idx_teacher_assignment (teacher_assignment_id),
    INDEX idx_lesson_hour (lesson_hour_id),
    INDEX idx_academic_year (academic_year_id),
    INDEX idx_day_of_week (day_of_week),
    INDEX idx_effective_dates (effective_start_date, effective_end_date),
    INDEX idx_status (status),
    INDEX idx_day_lesson_combo (day_of_week, lesson_hour_id),
    UNIQUE KEY unique_schedule_slot (teacher_assignment_id, lesson_hour_id, day_of_week, academic_year_id, effective_start_date),
    FOREIGN KEY (teacher_assignment_id) REFERENCES teacher_assignments(id) ON DELETE CASCADE,
    FOREIGN KEY (lesson_hour_id) REFERENCES lesson_hours(id) ON DELETE CASCADE,
    FOREIGN KEY (academic_year_id) REFERENCES academic_years(id) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
```

### 16. Schedule Exceptions Table (Enhanced)
```sql
CREATE TABLE schedule_exceptions (
    id BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    class_schedule_id BIGINT UNSIGNED NULL, -- NULL for global holidays
    academic_year_id BIGINT UNSIGNED NOT NULL,
    exception_date DATE NOT NULL,
    exception_type ENUM('holiday', 'exam', 'event', 'maintenance', 'teacher_leave', 'other') NOT NULL,
    reason VARCHAR(500) NOT NULL,
    replacement_schedule_id BIGINT UNSIGNED NULL,
    is_makeup_required BOOLEAN DEFAULT FALSE,
    makeup_date DATE NULL,
    status ENUM('scheduled', 'confirmed', 'cancelled') DEFAULT 'scheduled',
    created_by BIGINT UNSIGNED NULL,
    approved_by BIGINT UNSIGNED NULL,
    approved_at TIMESTAMP NULL,
    created_at TIMESTAMP NULL,
    updated_at TIMESTAMP NULL,
    
    INDEX idx_class_schedule (class_schedule_id),
    INDEX idx_academic_year (academic_year_id),
    INDEX idx_exception_date (exception_date),
    INDEX idx_exception_type (exception_type),
    INDEX idx_status (status),
    INDEX idx_makeup_date (makeup_date),
    UNIQUE KEY unique_schedule_exception_date (class_schedule_id, exception_date),
    FOREIGN KEY (class_schedule_id) REFERENCES class_schedules(id) ON DELETE CASCADE,
    FOREIGN KEY (academic_year_id) REFERENCES academic_years(id) ON DELETE CASCADE,
    FOREIGN KEY (replacement_schedule_id) REFERENCES class_schedules(id) ON DELETE SET NULL,
    FOREIGN KEY (created_by) REFERENCES users(id) ON DELETE SET NULL,
    FOREIGN KEY (approved_by) REFERENCES users(id) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
```

## Geofencing Tables

### 17. Geofences Table (Enhanced)
```sql
CREATE TABLE geofences (
    id BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(255) NOT NULL,
    code VARCHAR(50) UNIQUE NOT NULL,
    description TEXT NULL,
    latitude DECIMAL(10, 8) NOT NULL,
    longitude DECIMAL(11, 8) NOT NULL,
    radius DECIMAL(8, 2) NOT NULL, -- in meters
    address TEXT NULL,
    geofence_type ENUM('school', 'building', 'classroom', 'field', 'parking', 'other') DEFAULT 'school',
    is_active BOOLEAN DEFAULT TRUE,
    alert_on_entry BOOLEAN DEFAULT TRUE,
    alert_on_exit BOOLEAN DEFAULT TRUE,
    working_hours_start TIME NULL,
    working_hours_end TIME NULL,
    timezone VARCHAR(50) DEFAULT 'Asia/Jakarta',
    created_at TIMESTAMP NULL,
    updated_at TIMESTAMP NULL,
    
    INDEX idx_name (name),
    INDEX idx_code (code),
    INDEX idx_coordinates (latitude, longitude),
    INDEX idx_type (geofence_type),
    INDEX idx_active (is_active),
    CONSTRAINT chk_radius CHECK (radius > 0),
    CONSTRAINT chk_latitude CHECK (latitude BETWEEN -90 AND 90),
    CONSTRAINT chk_longitude CHECK (longitude BETWEEN -180 AND 180)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
```

### 18. Geofence Logs Table (Enhanced)
```sql
CREATE TABLE geofence_logs (
    id BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    teacher_id BIGINT UNSIGNED NOT NULL,
    geofence_id BIGINT UNSIGNED NOT NULL,
    action ENUM('enter', 'exit') NOT NULL,
    latitude DECIMAL(10, 8) NOT NULL,
    longitude DECIMAL(11, 8) NOT NULL,
    accuracy DECIMAL(5, 2) NULL, -- GPS accuracy in meters
    device_info JSON NULL, -- Device information
    logged_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    processed_at TIMESTAMP NULL,
    is_valid BOOLEAN DEFAULT TRUE,
    validation_notes TEXT NULL,
    created_at TIMESTAMP NULL,
    updated_at TIMESTAMP NULL,
    
    INDEX idx_teacher (teacher_id),
    INDEX idx_geofence (geofence_id),
    INDEX idx_action (action),
    INDEX idx_logged_at (logged_at),
    INDEX idx_processed_at (processed_at),
    INDEX idx_valid (is_valid),
    INDEX idx_teacher_date (teacher_id, logged_at),
    FOREIGN KEY (teacher_id) REFERENCES teachers(id) ON DELETE CASCADE,
    FOREIGN KEY (geofence_id) REFERENCES geofences(id) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
PARTITION BY RANGE (YEAR(logged_at)) (
    PARTITION p2024 VALUES LESS THAN (2025),
    PARTITION p2025 VALUES LESS THAN (2026),
    PARTITION p2026 VALUES LESS THAN (2027),
    PARTITION p_future VALUES LESS THAN MAXVALUE
);
```
