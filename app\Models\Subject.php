<?php

namespace App\Models;

use App\Enums\GeneralStatus;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;

class Subject extends Model
{
    protected $fillable = [
        'name',
        'code',
        'credit_hours',
        'description',
        'status'
    ];

    protected $casts = [
        'status' => GeneralStatus::class,
        'credit_hours' => 'integer',
    ];

    public function teacherAssignments(): HasMany
    {
        return $this->hasMany(TeacherAssignment::class);
    }

    public function scopeActive($query)
    {
        return $query->where('status', GeneralStatus::Active);
    }
}
