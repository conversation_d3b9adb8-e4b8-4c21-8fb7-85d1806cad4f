<?php

namespace App\Http\Controllers;

use App\Http\Requests\User\StoreUserRequest;
use App\Http\Requests\User\UpdateUserRequest;
use App\Models\User;
use Illuminate\Http\RedirectResponse;
use Illuminate\Http\Request;
use Illuminate\View\View;

class UserController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index(Request $request): View
    {
        $query = User::with(['teacher', 'student']);

        // Apply search if provided
        if ($request->filled('search')) {
            $search = $request->get('search');
            $query->where(function ($q) use ($search) {
                $q->where('name', 'like', "%{$search}%")
                    ->orWhere('username', 'like', "%{$search}%")
                    ->orWhere('email', 'like', "%{$search}%");
            });
        }

        // Apply status filter if provided
        if ($request->filled('status')) {
            $query->where('status', $request->get('status'));
        }

        // Apply role filter if provided
        if ($request->filled('role')) {
            $role = $request->get('role');
            switch ($role) {
                case 'teacher':
                    $query->whereHas('teacher');
                    break;
                case 'student':
                    $query->whereHas('student');
                    break;
                case 'admin':
                    $query->whereDoesntHave('teacher')
                        ->whereDoesntHave('student');
                    break;
            }
        }

        // Apply sorting
        $sortBy = $request->get('sort_by', 'created_at');
        $sortDirection = $request->get('sort_direction', 'desc');
        $query->orderBy($sortBy, $sortDirection);

        $users = $query->paginate(15);
        $users->appends($request->query());

        return view('users.index', compact('users'));
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create(): View
    {
        return view('users.create');
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(StoreUserRequest $request): RedirectResponse
    {
        try {
            $data = $request->validated();

            // Set default status if not provided
            if (!isset($data['status'])) {
                $data['status'] = true;
            }

            $user = User::create($data);

            // Log user creation
            logger()->info('User created', [
                'user_id' => $user->id,
                'username' => $user->username,
                'created_by' => auth()->id(),
            ]);

            return redirect()
                ->route('users.show', $user)
                ->with('success', 'User created successfully.');
        } catch (\Exception $e) {
            return back()
                ->withInput()
                ->with('error', 'Failed to create user: ' . $e->getMessage());
        }
    }

    /**
     * Display the specified resource.
     */
    public function show(User $user): View
    {
        $user->load(['teacher', 'student', 'contactInformation']);

        return view('users.show', compact('user'));
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(User $user): View
    {
        return view('users.edit', compact('user'));
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(UpdateUserRequest $request, User $user): RedirectResponse
    {
        try {
            $data = $request->validated();

            $user->update($data);

            // Log user update
            logger()->info('User updated', [
                'user_id' => $user->id,
                'username' => $user->username,
                'updated_by' => auth()->id(),
            ]);

            return redirect()
                ->route('users.show', $user)
                ->with('success', 'User updated successfully.');
        } catch (\Exception $e) {
            return back()
                ->withInput()
                ->with('error', 'Failed to update user: ' . $e->getMessage());
        }
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(User $user): RedirectResponse
    {
        try {
            // Check if user has related records that prevent deletion
            if ($user->teacher && $user->teacher->teacherAssignments()->exists()) {
                return back()->with('error', 'Cannot delete user with active teacher assignments.');
            }

            if ($user->student && $user->student->attendances()->exists()) {
                return back()->with('error', 'Cannot delete user with attendance records.');
            }

            // Log user deletion before deleting
            logger()->info('User deleted', [
                'user_id' => $user->id,
                'username' => $user->username,
                'deleted_by' => auth()->id(),
            ]);

            $user->delete();

            return redirect()
                ->route('users.index')
                ->with('success', 'User deleted successfully.');
        } catch (\Exception $e) {
            return back()
                ->with('error', 'Failed to delete user: ' . $e->getMessage());
        }
    }
}
