<?php

namespace App\Http\Requests\Student;

use Illuminate\Foundation\Http\FormRequest;

class StoreStudentRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true; // Adjust based on authorization logic
    }

    /**
     * Get the validation rules that apply to the request.
     */
    public function rules(): array
    {
        return [
            'program_id' => [
                'required',
                'exists:programs,id',
            ],
            'student_id' => [
                'required',
                'string',
                'max:50',
                'unique:students,student_id',
                'regex:/^[A-Z0-9_-]+$/',
            ],
            'nisn' => [
                'nullable',
                'string',
                'max:20',
                'unique:students,nisn',
                'regex:/^[0-9]+$/',
            ],
            'full_name' => [
                'required',
                'string',
                'max:255',
            ],
            'gender' => [
                'required',
                'in:male,female',
            ],
            'birth_date' => [
                'nullable',
                'date',
                'before:today',
            ],
            'birth_place' => [
                'nullable',
                'string',
                'max:100',
            ],
            'religion' => [
                'nullable',
                'in:islam,kristen,katolik,hindu,buddha,konghucu',
            ],
            'blood_type' => [
                'nullable',
                'in:A,B,AB,O',
            ],
            'enrollment_date' => [
                'required',
                'date',
                'before_or_equal:today',
            ],
            'status' => [
                'sometimes',
                'in:active,inactive,graduated,transferred,dropped',
            ],
            'notes' => [
                'nullable',
                'string',
            ],
        ];
    }

    /**
     * Get custom messages for validator errors.
     */
    public function messages(): array
    {
        return [
            'program_id.required' => 'Program is required.',
            'program_id.exists' => 'Selected program does not exist.',
            'student_id.required' => 'Student ID is required.',
            'student_id.unique' => 'This student ID is already taken.',
            'student_id.regex' => 'Student ID can only contain uppercase letters, numbers, underscores, and hyphens.',
            'nisn.unique' => 'This NISN is already taken.',
            'nisn.regex' => 'NISN can only contain numbers.',
            'full_name.required' => 'Full name is required.',
            'gender.required' => 'Gender is required.',
            'gender.in' => 'Gender must be either male or female.',
            'birth_date.date' => 'Please provide a valid birth date.',
            'birth_date.before' => 'Birth date must be before today.',
            'religion.in' => 'Please select a valid religion.',
            'blood_type.in' => 'Please select a valid blood type.',
            'enrollment_date.required' => 'Enrollment date is required.',
            'enrollment_date.date' => 'Please provide a valid enrollment date.',
            'enrollment_date.before_or_equal' => 'Enrollment date cannot be in the future.',
            'status.in' => 'Please select a valid status.',
        ];
    }

    /**
     * Get custom attributes for validator errors.
     */
    public function attributes(): array
    {
        return [
            'program_id' => 'program',
            'student_id' => 'student ID',
            'nisn' => 'NISN',
            'full_name' => 'full name',
            'birth_date' => 'birth date',
            'birth_place' => 'birth place',
            'enrollment_date' => 'enrollment date',
        ];
    }
}
