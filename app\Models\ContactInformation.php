<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\MorphTo;

class ContactInformation extends Model
{
    protected $fillable = [
        'contactable_type',
        'contactable_id',
        'phone',
        'email',
        'address',
        'city',
        'postal_code',
        'is_primary'
    ];

    protected $casts = [
        'is_primary' => 'boolean',
    ];

    public function contactable(): MorphTo
    {
        return $this->morphTo();
    }
}
