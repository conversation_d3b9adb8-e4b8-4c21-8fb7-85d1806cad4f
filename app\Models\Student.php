<?php

namespace App\Models;

use App\Enums\Gender;
use App\Enums\Religion;
use App\Enums\BloodType;
use App\Enums\StudentStatus;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\MorphMany;

class Student extends Model
{
    protected $fillable = [
        'program_id',
        'student_id',
        'nisn',
        'full_name',
        'gender',
        'birth_date',
        'birth_place',
        'religion',
        'blood_type',
        'enrollment_date',
        'status',
        'notes'
    ];

    protected $casts = [
        'gender' => Gender::class,
        'religion' => Religion::class,
        'blood_type' => BloodType::class,
        'status' => StudentStatus::class,
        'birth_date' => 'date',
        'enrollment_date' => 'date',
    ];

    public function program(): BelongsTo
    {
        return $this->belongsTo(Program::class);
    }

    public function contactInformation(): MorphMany
    {
        return $this->morphMany(ContactInformation::class, 'contactable');
    }

    public function parents(): BelongsToMany
    {
        return $this->belongsToMany(ParentModel::class, 'student_parents', 'student_id', 'parent_id')
            ->withPivot('relation_type', 'is_emergency_contact', 'notes')
            ->withTimestamps();
    }

    public function classrooms(): BelongsToMany
    {
        return $this->belongsToMany(Classroom::class, 'classroom_students')
            ->withPivot('enrollment_date', 'completion_date', 'is_active', 'notes')
            ->withTimestamps();
    }

    public function attendances(): HasMany
    {
        return $this->hasMany(StudentAttendance::class);
    }

    public function getDisplayNameAttribute(): string
    {
        return $this->full_name;
    }
}
