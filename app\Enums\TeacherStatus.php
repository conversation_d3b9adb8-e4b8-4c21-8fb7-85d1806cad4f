<?php

namespace App\Enums;

enum TeacherStatus: string
{
    case ACTIVE = 'active';
    case INACTIVE = 'inactive';
    case SUSPENDED = 'suspended';
    case TERMINATED = 'terminated';

    public function label(): string
    {
        return match($this) {
            self::ACTIVE => 'Aktif',
            self::INACTIVE => 'Tidak Aktif',
            self::SUSPENDED => 'Diskors',
            self::TERMINATED => 'Diberhentikan',
        };
    }

    public static function options(): array
    {
        return [
            self::ACTIVE->value => self::ACTIVE->label(),
            self::INACTIVE->value => self::INACTIVE->label(),
            self::SUSPENDED->value => self::SUSPENDED->label(),
            self::TERMINATED->value => self::TERMINATED->label(),
        ];
    }
}
