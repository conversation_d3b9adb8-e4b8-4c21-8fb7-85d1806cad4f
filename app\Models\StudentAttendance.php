<?php

namespace App\Models;

use App\Enums\StudentAttendanceStatus;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class StudentAttendance extends Model
{
    protected $fillable = [
        'student_id',
        'class_schedule_id',
        'attendance_date',
        'status',
        'check_in_time',
        'notes'
    ];

    protected $casts = [
        'status' => StudentAttendanceStatus::class,
        'attendance_date' => 'date',
        'check_in_time' => 'datetime:H:i',
    ];

    public function student(): BelongsTo
    {
        return $this->belongsTo(Student::class);
    }

    public function classSchedule(): BelongsTo
    {
        return $this->belongsTo(ClassSchedule::class);
    }

    public function scopePresent($query)
    {
        return $query->where('status', StudentAttendanceStatus::Present);
    }

    public function scopeForDate($query, $date)
    {
        return $query->where('attendance_date', $date);
    }
}
