<?php

namespace App\Models;

use App\Enums\EmploymentStatus;
use App\Enums\TeacherStatus;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\MorphMany;

class Teacher extends Model
{
    protected $fillable = [
        'user_id',
        'shift_id',
        'nip',
        'employee_id',
        'employment_status',
        'hire_date',
        'salary',
        'status',
        'notes'
    ];

    protected $casts = [
        'employment_status' => EmploymentStatus::class,
        'status' => TeacherStatus::class,
        'hire_date' => 'date',
        'salary' => 'decimal:2',
    ];

    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    public function shift(): BelongsTo
    {
        return $this->belongsTo(Shift::class);
    }

    public function teacherAssignments(): HasMany
    {
        return $this->hasMany(TeacherAssignment::class);
    }

    public function attendances(): Has<PERSON>any
    {
        return $this->hasMany(TeacherAttendance::class);
    }

    public function salaryComponents(): HasMany
    {
        return $this->hasMany(TeacherSalaryComponent::class);
    }

    public function payrollRecords(): HasMany
    {
        return $this->hasMany(PayrollRecord::class);
    }

    public function contactInformation(): MorphMany
    {
        return $this->morphMany(ContactInformation::class, 'contactable');
    }

    public function getFullNameAttribute(): string
    {
        return $this->user ? $this->user->full_name : '';
    }

    public function scopeActive($query)
    {
        return $query->where('status', TeacherStatus::Active);
    }
}
