<?php

namespace App\Enums;

enum StudentStatus: string
{
    case ACTIVE = 'active';
    case INACTIVE = 'inactive';
    case GRADUATED = 'graduated';
    case TRANSFERRED = 'transferred';
    case DROPPED = 'dropped';

    public function label(): string
    {
        return match($this) {
            self::ACTIVE => 'Aktif',
            self::INACTIVE => 'Tidak Aktif',
            self::GRADUATED => 'Lulus',
            self::TRANSFERRED => 'Pindah',
            self::DROPPED => 'Keluar',
        };
    }

    public static function options(): array
    {
        return [
            self::ACTIVE->value => self::ACTIVE->label(),
            self::INACTIVE->value => self::INACTIVE->label(),
            self::GRADUATED->value => self::GRADUATED->label(),
            self::TRANSFERRED->value => self::TRANSFERRED->label(),
            self::DROPPED->value => self::DROPPED->label(),
        ];
    }
}
