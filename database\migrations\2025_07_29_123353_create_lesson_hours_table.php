<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('lesson_hours', function (Blueprint $table) {
            $table->id();
            $table->string('name', 100);
            $table->unsignedTinyInteger('hour_number');
            $table->time('start_time');
            $table->time('end_time');
            $table->enum('status', ['active', 'inactive', 'archived'])->default('active');
            $table->timestamps();

            // Indexes
            $table->index('hour_number');
            $table->index('status');
            $table->index(['start_time', 'end_time']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('lesson_hours');
    }
};
