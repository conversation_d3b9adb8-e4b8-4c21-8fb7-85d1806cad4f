<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('classrooms', function (Blueprint $table) {
            $table->id();
            $table->foreignId('academic_year_id')->constrained()->onDelete('restrict');
            $table->foreignId('program_id')->constrained()->onDelete('restrict');
            $table->string('name', 100);
            $table->string('code', 20);
            $table->unsignedTinyInteger('grade_level');
            $table->unsignedSmallInteger('capacity')->default(30);
            $table->string('room_location', 100)->nullable();
            $table->enum('status', ['active', 'inactive', 'archived'])->default('active');
            $table->text('description')->nullable();
            $table->timestamps();

            // Indexes
            $table->index('academic_year_id');
            $table->index('program_id');
            $table->index('status');
            $table->index('grade_level');
            $table->unique(['academic_year_id', 'code'], 'unique_classroom_code_per_year');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('classrooms');
    }
};
