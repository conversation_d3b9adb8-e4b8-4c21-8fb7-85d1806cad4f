<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use Spatie\Permission\Models\Role;
use Spatie\Permission\Models\Permission;

class RolePermissionSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Reset cached roles and permissions
        app()[\Spatie\Permission\PermissionRegistrar::class]->forgetCachedPermissions();

        // Create permissions for each entity
        $entities = ['users', 'teachers', 'students', 'programs', 'subjects', 'shifts', 'classrooms'];
        $actions = ['view', 'create', 'edit', 'delete'];

        foreach ($entities as $entity) {
            foreach ($actions as $action) {
                Permission::create(['name' => "{$action} {$entity}"]);
            }
        }

        // Additional specific permissions
        Permission::create(['name' => 'manage system']);
        Permission::create(['name' => 'view reports']);
        Permission::create(['name' => 'manage attendance']);
        Permission::create(['name' => 'manage grades']);
        Permission::create(['name' => 'view own profile']);
        Permission::create(['name' => 'edit own profile']);

        // Create roles and assign permissions
        $adminRole = Role::create(['name' => 'admin']);
        $teacherRole = Role::create(['name' => 'teacher']);
        $studentRole = Role::create(['name' => 'student']);

        // Admin gets all permissions
        $adminRole->givePermissionTo(Permission::all());

        // Teacher permissions
        $teacherRole->givePermissionTo([
            'view users',
            'view teachers',
            'view students',
            'view programs',
            'view subjects',
            'view shifts',
            'view classrooms',
            'edit students',
            'manage attendance',
            'manage grades',
            'view reports',
            'view own profile',
            'edit own profile',
        ]);

        // Student permissions
        $studentRole->givePermissionTo([
            'view own profile',
            'edit own profile',
        ]);
    }
}
