<?php

namespace App\Http\Requests\Program;

use Illuminate\Foundation\Http\FormRequest;

class UpdateProgramRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true; // Adjust based on authorization logic
    }

    /**
     * Get the validation rules that apply to the request.
     */
    public function rules(): array
    {
        $programId = $this->route('program')?->id;

        return [
            'name' => [
                'required',
                'string',
                'max:100',
            ],
            'code' => [
                'required',
                'string',
                'max:20',
                'unique:programs,code,' . $programId,
                'regex:/^[A-Z0-9_-]+$/',
            ],
            'education_level' => [
                'required',
                'in:SD,SMP,SMA,D3,S1,S2,S3',
            ],
            'description' => [
                'nullable',
                'string',
            ],
            'duration_years' => [
                'required',
                'integer',
                'min:1',
                'max:10',
            ],
            'status' => [
                'required',
                'in:active,inactive,archived',
            ],
        ];
    }

    /**
     * Get custom messages for validator errors.
     */
    public function messages(): array
    {
        return [
            'name.required' => 'Program name is required.',
            'name.max' => 'Program name cannot exceed 100 characters.',
            'code.required' => 'Program code is required.',
            'code.unique' => 'This program code is already taken.',
            'code.regex' => 'Program code can only contain uppercase letters, numbers, underscores, and hyphens.',
            'education_level.required' => 'Education level is required.',
            'education_level.in' => 'Please select a valid education level.',
            'duration_years.required' => 'Duration in years is required.',
            'duration_years.integer' => 'Duration must be a whole number.',
            'duration_years.min' => 'Duration must be at least 1 year.',
            'duration_years.max' => 'Duration cannot exceed 10 years.',
            'status.required' => 'Status is required.',
            'status.in' => 'Please select a valid status.',
        ];
    }

    /**
     * Get custom attributes for validator errors.
     */
    public function attributes(): array
    {
        return [
            'name' => 'program name',
            'code' => 'program code',
            'education_level' => 'education level',
            'duration_years' => 'duration in years',
        ];
    }
}
