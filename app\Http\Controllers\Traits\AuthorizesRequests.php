<?php

namespace App\Http\Controllers\Traits;

use Illuminate\Http\Request;

trait AuthorizesRequests
{
    /**
     * Check if the current user can view the resource.
     */
    protected function authorizeView(string $resource): void
    {
        if (!auth()->user()->can("view {$resource}")) {
            abort(403, "You don't have permission to view {$resource}.");
        }
    }

    /**
     * Check if the current user can create the resource.
     */
    protected function authorizeCreate(string $resource): void
    {
        if (!auth()->user()->can("create {$resource}")) {
            abort(403, "You don't have permission to create {$resource}.");
        }
    }

    /**
     * Check if the current user can edit the resource.
     */
    protected function authorizeEdit(string $resource): void
    {
        if (!auth()->user()->can("edit {$resource}")) {
            abort(403, "You don't have permission to edit {$resource}.");
        }
    }

    /**
     * Check if the current user can delete the resource.
     */
    protected function authorizeDelete(string $resource): void
    {
        if (!auth()->user()->can("delete {$resource}")) {
            abort(403, "You don't have permission to delete {$resource}.");
        }
    }

    /**
     * Check if the current user has admin role.
     */
    protected function requireAdmin(): void
    {
        if (!auth()->user()->hasRole('admin')) {
            abort(403, 'Admin access required.');
        }
    }

    /**
     * Check if the current user has teacher or admin role.
     */
    protected function requireTeacherOrAdmin(): void
    {
        if (!auth()->user()->hasAnyRole(['teacher', 'admin'])) {
            abort(403, 'Teacher or admin access required.');
        }
    }

    /**
     * Check if the current user can access their own profile or is admin.
     */
    protected function authorizeOwnProfileOrAdmin($userId): void
    {
        $currentUser = auth()->user();
        
        if ($currentUser->id !== (int) $userId && !$currentUser->hasRole('admin')) {
            abort(403, 'You can only access your own profile.');
        }
    }
}
