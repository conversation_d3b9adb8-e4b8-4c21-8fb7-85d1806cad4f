# Optimized Laravel Migrations

This document contains the Laravel migration files for the optimized database schema. These migrations should be run in the specified order to maintain foreign key dependencies.

## Migration Order

1. Core tables: `users`, `academic_years`, `programs`, `shifts`, `subjects`, `lesson_hours`
2. Personnel tables: `teachers`, `parents`, `students`
3. Relationship tables: `student_parents`, `contact_information`
4. Academic tables: `classrooms`, `classroom_students`, `teacher_assignments`
5. Scheduling tables: `class_schedules`, `schedule_exceptions`
6. Location tables: `geofences`, `geofence_logs`
7. Attendance tables: `teacher_attendances`, `student_attendances`
8. Payroll tables: `teacher_salary_components`, `payroll_records`
9. Supporting tables: `audit_logs`, `system_settings`, `notification_templates`

---

## 1. Enhanced Users Migration

```php
<?php
// database/migrations/2024_01_01_000001_create_optimized_users_table.php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::create('users', function (Blueprint $table) {
            $table->id();
            $table->string('username', 100)->unique();
            $table->string('name');
            $table->string('email')->unique();
            $table->string('photo_path', 500)->nullable();
            $table->string('password');
            $table->timestamp('email_verified_at')->nullable();
            $table->boolean('status')->default(true);
            $table->timestamp('last_login_at')->nullable();
            $table->unsignedTinyInteger('login_attempts')->default(0);
            $table->timestamp('locked_until')->nullable();
            $table->rememberToken();
            $table->timestamps();
            $table->softDeletes();

            // Indexes
            $table->index('email');
            $table->index('username');
            $table->index('status');
            $table->index('last_login_at');
        });
    }

    public function down(): void
    {
        Schema::dropIfExists('users');
    }
};
```

## 2. Contact Information Migration

```php
<?php
// database/migrations/2024_01_01_000002_create_contact_information_table.php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::create('contact_information', function (Blueprint $table) {
            $table->id();
            $table->enum('entity_type', ['user', 'teacher', 'parent', 'student']);
            $table->unsignedBigInteger('entity_id');
            $table->string('phone_primary', 20)->nullable();
            $table->string('phone_secondary', 20)->nullable();
            $table->string('email_secondary')->nullable();
            $table->text('address_line_1')->nullable();
            $table->text('address_line_2')->nullable();
            $table->string('city', 100)->nullable();
            $table->string('state', 100)->nullable();
            $table->string('postal_code', 20)->nullable();
            $table->string('country', 100)->default('Indonesia');
            $table->boolean('is_primary')->default(true);
            $table->timestamps();

            // Indexes
            $table->index(['entity_type', 'entity_id']);
            $table->index('phone_primary');
            $table->unique(['entity_type', 'entity_id', 'is_primary'], 'unique_primary_contact');
        });
    }

    public function down(): void
    {
        Schema::dropIfExists('contact_information');
    }
};
```

## 3. Academic Years Migration

```php
<?php
// database/migrations/2024_01_01_000003_create_academic_years_table.php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::create('academic_years', function (Blueprint $table) {
            $table->id();
            $table->string('name', 100);
            $table->string('code', 20)->unique();
            $table->enum('semester', ['odd', 'even']);
            $table->date('start_date');
            $table->date('end_date');
            $table->date('registration_start')->nullable();
            $table->date('registration_end')->nullable();
            $table->enum('status', ['planned', 'active', 'completed', 'cancelled'])->default('planned');
            $table->text('description')->nullable();
            $table->timestamps();

            // Indexes
            $table->index('status');
            $table->index(['start_date', 'end_date']);
            $table->index('code');

            // Check constraints (handled in raw SQL or model validation)
        });

        // Add check constraints using raw SQL
        DB::statement('ALTER TABLE academic_years ADD CONSTRAINT chk_academic_year_dates CHECK (end_date > start_date)');
        DB::statement('ALTER TABLE academic_years ADD CONSTRAINT chk_registration_dates CHECK (registration_end >= registration_start OR registration_end IS NULL OR registration_start IS NULL)');
    }

    public function down(): void
    {
        Schema::dropIfExists('academic_years');
    }
};
```

## 4. Programs Migration

```php
<?php
// database/migrations/2024_01_01_000004_create_programs_table.php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::create('programs', function (Blueprint $table) {
            $table->id();
            $table->string('name');
            $table->string('code', 50)->unique();
            $table->string('short_name', 100)->nullable();
            $table->text('description')->nullable();
            $table->unsignedTinyInteger('duration_years')->default(3);
            $table->unsignedSmallInteger('min_students')->default(1);
            $table->unsignedSmallInteger('max_students')->default(40);
            $table->enum('status', ['active', 'inactive', 'archived'])->default('active');
            $table->unsignedSmallInteger('sort_order')->default(0);
            $table->timestamps();

            // Indexes
            $table->index('status');
            $table->index('code');
            $table->index('sort_order');
        });
    }

    public function down(): void
    {
        Schema::dropIfExists('programs');
    }
};
```

## 5. Shifts Migration

```php
<?php
// database/migrations/2024_01_01_000005_create_shifts_table.php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::create('shifts', function (Blueprint $table) {
            $table->id();
            $table->string('name', 100);
            $table->string('code', 20)->unique();
            $table->time('start_time');
            $table->time('end_time');
            $table->time('break_start')->nullable();
            $table->time('break_end')->nullable();
            $table->text('description')->nullable();
            $table->enum('status', ['active', 'inactive'])->default('active');
            $table->unsignedSmallInteger('sort_order')->default(0);
            $table->timestamps();

            // Indexes
            $table->index('status');
            $table->index(['start_time', 'end_time']);
            $table->index('code');
        });

        // Add check constraint
        DB::statement('ALTER TABLE shifts ADD CONSTRAINT chk_shift_times CHECK (end_time > start_time)');
    }

    public function down(): void
    {
        Schema::dropIfExists('shifts');
    }
};
```

## 6. Subjects Migration

```php
<?php
// database/migrations/2024_01_01_000006_create_subjects_table.php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::create('subjects', function (Blueprint $table) {
            $table->id();
            $table->foreignId('program_id')->constrained()->onDelete('cascade');
            $table->string('name');
            $table->string('code', 50);
            $table->string('short_name', 100)->nullable();
            $table->text('description')->nullable();
            $table->decimal('credit_hours', 3, 1)->default(1.0);
            $table->boolean('is_mandatory')->default(true);
            $table->json('grade_levels')->nullable(); // ['7', '8', '9']
            $table->enum('status', ['active', 'inactive'])->default('active');
            $table->unsignedSmallInteger('sort_order')->default(0);
            $table->timestamps();

            // Indexes
            $table->index('program_id');
            $table->index('code');
            $table->index('status');
            $table->unique(['program_id', 'code'], 'unique_subject_code');
        });
    }

    public function down(): void
    {
        Schema::dropIfExists('subjects');
    }
};
```

## 7. Lesson Hours Migration

```php
<?php
// database/migrations/2024_01_01_000007_create_lesson_hours_table.php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::create('lesson_hours', function (Blueprint $table) {
            $table->id();
            $table->foreignId('shift_id')->nullable()->constrained()->onDelete('set null');
            $table->string('name', 100);
            $table->unsignedSmallInteger('sequence');
            $table->time('start_time');
            $table->time('end_time');
            $table->boolean('is_break')->default(false);
            $table->enum('status', ['active', 'inactive'])->default('active');
            $table->timestamps();

            // Indexes
            $table->index('shift_id');
            $table->index('sequence');
            $table->index(['start_time', 'end_time']);
            $table->unique(['shift_id', 'start_time', 'end_time'], 'unique_lesson_time');
        });

        // Add check constraint
        DB::statement('ALTER TABLE lesson_hours ADD CONSTRAINT chk_lesson_times CHECK (end_time > start_time)');
    }

    public function down(): void
    {
        Schema::dropIfExists('lesson_hours');
    }
};
```

## 8. Teachers Migration

```php
<?php
// database/migrations/2024_01_01_000008_create_teachers_table.php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::create('teachers', function (Blueprint $table) {
            $table->id();
            $table->foreignId('user_id')->unique()->constrained()->onDelete('cascade');
            $table->string('employee_id', 50)->unique()->nullable();
            $table->string('nip', 50)->unique()->nullable();
            $table->string('birth_place');
            $table->date('birth_date');
            $table->enum('gender', ['male', 'female']);
            $table->enum('education_level', ['D3', 'S1', 'S2', 'S3'])->nullable();
            $table->string('specialization')->nullable();
            $table->date('hire_date')->nullable();
            $table->enum('employment_status', ['permanent', 'contract', 'temporary', 'probation'])->default('contract');
            $table->boolean('gps_consent')->default(false);
            $table->timestamp('gps_consent_given_at')->nullable();
            $table->string('emergency_contact_name')->nullable();
            $table->string('emergency_contact_phone', 20)->nullable();
            $table->enum('status', ['active', 'inactive', 'suspended', 'terminated'])->default('active');
            $table->timestamps();
            $table->softDeletes();

            // Indexes
            $table->index('user_id');
            $table->index('employee_id');
            $table->index('nip');
            $table->index('status');
            $table->index('employment_status');
        });
    }

    public function down(): void
    {
        Schema::dropIfExists('teachers');
    }
};
```

## 9. Parents Migration

```php
<?php
// database/migrations/2024_01_01_000009_create_parents_table.php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::create('parents', function (Blueprint $table) {
            $table->id();
            $table->foreignId('user_id')->nullable()->constrained()->onDelete('set null');
            $table->string('parent_code', 50)->unique()->nullable();
            $table->string('name');
            $table->string('id_number', 50)->nullable(); // KTP/ID Card number
            $table->string('occupation')->nullable();
            $table->string('workplace')->nullable();
            $table->enum('education_level', ['SD', 'SMP', 'SMA', 'D3', 'S1', 'S2', 'S3'])->nullable();
            $table->enum('monthly_income', ['< 1jt', '1-3jt', '3-5jt', '5-10jt', '> 10jt'])->nullable();
            $table->enum('status', ['active', 'inactive'])->default('active');
            $table->timestamps();
            $table->softDeletes();

            // Indexes
            $table->index('user_id');
            $table->index('parent_code');
            $table->index('name');
            $table->index('status');
        });
    }

    public function down(): void
    {
        Schema::dropIfExists('parents');
    }
};
```
