<?php

use App\Http\Controllers\ProfileController;
use App\Http\Controllers\UserController;
use App\Http\Controllers\TeacherController;
use App\Http\Controllers\StudentController;
use App\Http\Controllers\ProgramController;
use App\Http\Controllers\SubjectController;
use App\Http\Controllers\ShiftController;
use Illuminate\Support\Facades\Route;

Route::get('/', function () {
    return view('welcome');
});

Route::get('/dashboard', function () {
    return view('dashboard');
})->middleware(['auth', 'verified'])->name('dashboard');

Route::middleware('auth')->group(function () {
    Route::get('/profile', [ProfileController::class, 'edit'])->name('profile.edit');
    Route::patch('/profile', [ProfileController::class, 'update'])->name('profile.update');
    Route::delete('/profile', [ProfileController::class, 'destroy'])->name('profile.destroy');
});

// Admin-only routes
Route::middleware(['auth', 'role:admin'])->group(function () {
    // User management
    Route::resource('users', UserController::class);

    // Program management
    Route::resource('programs', ProgramController::class);

    // Subject management
    Route::resource('subjects', SubjectController::class);

    // Shift management
    Route::resource('shifts', ShiftController::class);
});

// Admin and Teacher routes
Route::middleware(['auth', 'role:admin,teacher'])->group(function () {
    // Teacher management
    Route::resource('teachers', TeacherController::class);

    // Student management
    Route::resource('students', StudentController::class);
});

require __DIR__ . '/auth.php';
