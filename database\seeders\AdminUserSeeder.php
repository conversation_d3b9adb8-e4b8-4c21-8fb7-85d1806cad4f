<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\User;
use Illuminate\Support\Facades\Hash;

class AdminUserSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Create admin user
        $admin = User::create([
            'username' => 'admin',
            'name' => 'System Administrator',
            'email' => '<EMAIL>',
            'password' => Hash::make('password'),
            'status' => true,
            'email_verified_at' => now(),
        ]);

        // Assign admin role
        $admin->assignRole('admin');

        // Create teacher user
        $teacher = User::create([
            'username' => 'teacher1',
            'name' => 'John Teacher',
            'email' => '<EMAIL>',
            'password' => Hash::make('password'),
            'status' => true,
            'email_verified_at' => now(),
        ]);

        // Assign teacher role
        $teacher->assignRole('teacher');

        // Create student user
        $student = User::create([
            'username' => 'student1',
            'name' => 'Jane Student',
            'email' => '<EMAIL>',
            'password' => Hash::make('password'),
            'status' => true,
            'email_verified_at' => now(),
        ]);

        // Assign student role
        $student->assignRole('student');
    }
}
