<?php

namespace App\Enums;

enum PayrollStatus: string
{
    case DRAFT = 'draft';
    case CALCULATED = 'calculated';
    case APPROVED = 'approved';
    case PAID = 'paid';
    case CANCELLED = 'cancelled';

    public function label(): string
    {
        return match($this) {
            self::DRAFT => 'Draft',
            self::CALCULATED => 'Dihitung',
            self::APPROVED => 'Disetujui',
            self::PAID => 'Dibayar',
            self::CANCELLED => 'Dibatalkan',
        };
    }

    public static function options(): array
    {
        return [
            self::DRAFT->value => self::DRAFT->label(),
            self::CALCULATED->value => self::CALCULATED->label(),
            self::APPROVED->value => self::APPROVED->label(),
            self::PAID->value => self::PAID->label(),
            self::CANCELLED->value => self::CANCELLED->label(),
        ];
    }
}
