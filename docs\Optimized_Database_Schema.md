# Optimized Database Schema for `db_rawooh_v2`

This document presents the optimized database schema that addresses normalization issues, performance bottlenecks, and data integrity concerns identified in the analysis.

## Key Improvements Made

### 1. **Normalization Fixes**

-   Created `contact_information` table to eliminate phone/address redundancy
-   Standardized phone number field lengths across all tables
-   Removed duplicate name fields where user relationship exists

### 2. **Performance Optimizations**

-   Added composite indexes for common query patterns
-   Optimized data types and field sizes
-   Added partitioning recommendations for large tables

### 3. **Data Integrity Enhancements**

-   Added check constraints for business rules
-   Created audit trail system
-   Added soft delete support for critical entities

### 4. **New Supporting Tables**

-   `contact_information` - Centralized contact data
-   `audit_logs` - Complete audit trail
-   `system_settings` - Application configuration
-   `notification_templates` - Message templates

---

## Core Tables (Optimized)

### 1. Users Table (Enhanced)

```sql
CREATE TABLE users (
    id BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    username VARCHAR(100) UNIQUE NOT NULL,
    name VARCHAR(255) NOT NULL,
    email VARCHAR(255) UNIQUE NOT NULL,
    photo_path VARCHAR(500) NULL,
    password VARCHAR(255) NOT NULL,
    email_verified_at TIMESTAMP NULL,
    status BOOLEAN DEFAULT TRUE,
    last_login_at TIMESTAMP NULL,
    login_attempts TINYINT UNSIGNED DEFAULT 0,
    locked_until TIMESTAMP NULL,
    remember_token VARCHAR(100) NULL,
    created_at TIMESTAMP NULL,
    updated_at TIMESTAMP NULL,
    deleted_at TIMESTAMP NULL,

    INDEX idx_email (email),
    INDEX idx_username (username),
    INDEX idx_status (status),
    INDEX idx_last_login (last_login_at)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
```

### 2. Contact Information Table (New)

```sql
CREATE TABLE contact_information (
    id BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    entity_type ENUM('user', 'teacher', 'parent', 'student') NOT NULL,
    entity_id BIGINT UNSIGNED NOT NULL,
    phone_primary VARCHAR(20) NULL,
    phone_secondary VARCHAR(20) NULL,
    email_secondary VARCHAR(255) NULL,
    address_line_1 TEXT NULL,
    address_line_2 TEXT NULL,
    city VARCHAR(100) NULL,
    state VARCHAR(100) NULL,
    postal_code VARCHAR(20) NULL,
    country VARCHAR(100) DEFAULT 'Indonesia',
    is_primary BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP NULL,
    updated_at TIMESTAMP NULL,

    INDEX idx_entity (entity_type, entity_id),
    INDEX idx_phone_primary (phone_primary),
    UNIQUE KEY unique_primary_contact (entity_type, entity_id, is_primary)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
```

### 3. Academic Years Table (Enhanced)

```sql
CREATE TABLE academic_years (
    id BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(100) NOT NULL,
    code VARCHAR(20) UNIQUE NOT NULL,
    semester ENUM('odd', 'even') NOT NULL,
    start_date DATE NOT NULL,
    end_date DATE NOT NULL,
    registration_start DATE NULL,
    registration_end DATE NULL,
    status ENUM('planned', 'active', 'completed', 'cancelled') DEFAULT 'planned',
    description TEXT NULL,
    created_at TIMESTAMP NULL,
    updated_at TIMESTAMP NULL,

    INDEX idx_status (status),
    INDEX idx_dates (start_date, end_date),
    INDEX idx_code (code),
    CONSTRAINT chk_academic_year_dates CHECK (end_date > start_date),
    CONSTRAINT chk_registration_dates CHECK (registration_end >= registration_start)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
```

### 4. Programs Table (Enhanced)

```sql
CREATE TABLE programs (
    id BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(255) NOT NULL,
    code VARCHAR(50) UNIQUE NOT NULL,
    short_name VARCHAR(100) NULL,
    description TEXT NULL,
    duration_years TINYINT UNSIGNED DEFAULT 3,
    min_students SMALLINT UNSIGNED DEFAULT 1,
    max_students SMALLINT UNSIGNED DEFAULT 40,
    status ENUM('active', 'inactive', 'archived') DEFAULT 'active',
    sort_order SMALLINT UNSIGNED DEFAULT 0,
    created_at TIMESTAMP NULL,
    updated_at TIMESTAMP NULL,

    INDEX idx_status (status),
    INDEX idx_code (code),
    INDEX idx_sort_order (sort_order)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
```

### 5. Shifts Table (Enhanced)

```sql
CREATE TABLE shifts (
    id BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(100) NOT NULL,
    code VARCHAR(20) UNIQUE NOT NULL,
    start_time TIME NOT NULL,
    end_time TIME NOT NULL,
    break_start TIME NULL,
    break_end TIME NULL,
    description TEXT NULL,
    status ENUM('active', 'inactive') DEFAULT 'active',
    sort_order SMALLINT UNSIGNED DEFAULT 0,
    created_at TIMESTAMP NULL,
    updated_at TIMESTAMP NULL,

    INDEX idx_status (status),
    INDEX idx_times (start_time, end_time),
    INDEX idx_code (code),
    CONSTRAINT chk_shift_times CHECK (end_time > start_time)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
```

### 6. Subjects Table (Enhanced)

```sql
CREATE TABLE subjects (
    id BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    program_id BIGINT UNSIGNED NOT NULL,
    name VARCHAR(255) NOT NULL,
    code VARCHAR(50) NOT NULL,
    short_name VARCHAR(100) NULL,
    description TEXT NULL,
    credit_hours DECIMAL(3,1) DEFAULT 1.0,
    is_mandatory BOOLEAN DEFAULT TRUE,
    grade_levels JSON NULL, -- ['7', '8', '9']
    status ENUM('active', 'inactive') DEFAULT 'active',
    sort_order SMALLINT UNSIGNED DEFAULT 0,
    created_at TIMESTAMP NULL,
    updated_at TIMESTAMP NULL,

    INDEX idx_program (program_id),
    INDEX idx_code (code),
    INDEX idx_status (status),
    UNIQUE KEY unique_subject_code (program_id, code),
    FOREIGN KEY (program_id) REFERENCES programs(id) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
```

### 7. Lesson Hours Table (Enhanced)

```sql
CREATE TABLE lesson_hours (
    id BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    shift_id BIGINT UNSIGNED NULL,
    name VARCHAR(100) NOT NULL,
    sequence SMALLINT UNSIGNED NOT NULL,
    start_time TIME NOT NULL,
    end_time TIME NOT NULL,
    duration_minutes SMALLINT UNSIGNED GENERATED ALWAYS AS (
        TIME_TO_SEC(end_time) - TIME_TO_SEC(start_time)
    ) / 60 STORED,
    is_break BOOLEAN DEFAULT FALSE,
    status ENUM('active', 'inactive') DEFAULT 'active',
    created_at TIMESTAMP NULL,
    updated_at TIMESTAMP NULL,

    INDEX idx_shift (shift_id),
    INDEX idx_sequence (sequence),
    INDEX idx_times (start_time, end_time),
    UNIQUE KEY unique_lesson_time (shift_id, start_time, end_time),
    CONSTRAINT chk_lesson_times CHECK (end_time > start_time),
    FOREIGN KEY (shift_id) REFERENCES shifts(id) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
```

### 8. Teachers Table (Optimized)

```sql
CREATE TABLE teachers (
    id BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    user_id BIGINT UNSIGNED UNIQUE NOT NULL,
    employee_id VARCHAR(50) UNIQUE NULL,
    nip VARCHAR(50) UNIQUE NULL,
    birth_place VARCHAR(255) NOT NULL,
    birth_date DATE NOT NULL,
    gender ENUM('male', 'female') NOT NULL,
    education_level ENUM('D3', 'S1', 'S2', 'S3') NULL,
    specialization VARCHAR(255) NULL,
    hire_date DATE NULL,
    employment_status ENUM('permanent', 'contract', 'temporary', 'probation') DEFAULT 'contract',
    gps_consent BOOLEAN DEFAULT FALSE,
    gps_consent_given_at TIMESTAMP NULL,
    emergency_contact_name VARCHAR(255) NULL,
    emergency_contact_phone VARCHAR(20) NULL,
    status ENUM('active', 'inactive', 'suspended', 'terminated') DEFAULT 'active',
    created_at TIMESTAMP NULL,
    updated_at TIMESTAMP NULL,
    deleted_at TIMESTAMP NULL,

    INDEX idx_user (user_id),
    INDEX idx_employee_id (employee_id),
    INDEX idx_nip (nip),
    INDEX idx_status (status),
    INDEX idx_employment_status (employment_status),
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
```

### 9. Parents Table (Optimized)

```sql
CREATE TABLE parents (
    id BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    user_id BIGINT UNSIGNED NULL,
    parent_code VARCHAR(50) UNIQUE NULL,
    name VARCHAR(255) NOT NULL,
    id_number VARCHAR(50) NULL, -- KTP/ID Card number
    occupation VARCHAR(255) NULL,
    workplace VARCHAR(255) NULL,
    education_level ENUM('SD', 'SMP', 'SMA', 'D3', 'S1', 'S2', 'S3') NULL,
    monthly_income ENUM('< 1jt', '1-3jt', '3-5jt', '5-10jt', '> 10jt') NULL,
    status ENUM('active', 'inactive') DEFAULT 'active',
    created_at TIMESTAMP NULL,
    updated_at TIMESTAMP NULL,
    deleted_at TIMESTAMP NULL,

    INDEX idx_user (user_id),
    INDEX idx_parent_code (parent_code),
    INDEX idx_name (name),
    INDEX idx_status (status),
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
```

### 10. Students Table (Optimized)

```sql
CREATE TABLE students (
    id BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    user_id BIGINT UNSIGNED UNIQUE NOT NULL,
    nis VARCHAR(20) UNIQUE NOT NULL,
    nisn VARCHAR(20) UNIQUE NOT NULL,
    student_code VARCHAR(50) UNIQUE NULL,
    birth_place VARCHAR(255) NOT NULL,
    birth_date DATE NOT NULL,
    gender ENUM('male', 'female') NOT NULL,
    religion ENUM('islam', 'kristen', 'katolik', 'hindu', 'buddha', 'konghucu') NOT NULL,
    blood_type ENUM('A', 'B', 'AB', 'O') NULL,
    entry_year YEAR NOT NULL,
    entry_date DATE NULL,
    graduation_date DATE NULL,
    profile_picture VARCHAR(500) NULL,
    medical_conditions TEXT NULL,
    allergies TEXT NULL,
    special_needs TEXT NULL,
    status ENUM('active', 'graduated', 'transferred', 'dropped_out', 'suspended') DEFAULT 'active',
    created_at TIMESTAMP NULL,
    updated_at TIMESTAMP NULL,
    deleted_at TIMESTAMP NULL,

    INDEX idx_user (user_id),
    INDEX idx_nis (nis),
    INDEX idx_nisn (nisn),
    INDEX idx_student_code (student_code),
    INDEX idx_entry_year (entry_year),
    INDEX idx_status (status),
    INDEX idx_religion (religion),
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
```

### 11. Student Parents Table (Enhanced)

```sql
CREATE TABLE student_parents (
    id BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    student_id BIGINT UNSIGNED NOT NULL,
    parent_id BIGINT UNSIGNED NOT NULL,
    relation_type ENUM('father', 'mother', 'guardian', 'stepfather', 'stepmother', 'grandfather', 'grandmother', 'other') NOT NULL,
    is_primary_contact BOOLEAN DEFAULT FALSE,
    is_emergency_contact BOOLEAN DEFAULT FALSE,
    financial_responsibility BOOLEAN DEFAULT FALSE,
    custody_rights BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP NULL,
    updated_at TIMESTAMP NULL,

    INDEX idx_student (student_id),
    INDEX idx_parent (parent_id),
    INDEX idx_relation (relation_type),
    INDEX idx_primary_contact (is_primary_contact),
    UNIQUE KEY unique_student_parent (student_id, parent_id),
    FOREIGN KEY (student_id) REFERENCES students(id) ON DELETE CASCADE,
    FOREIGN KEY (parent_id) REFERENCES parents(id) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
```
