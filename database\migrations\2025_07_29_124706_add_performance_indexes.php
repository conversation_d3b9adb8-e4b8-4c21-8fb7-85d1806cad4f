<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Composite indexes for common query patterns
        Schema::table('teacher_attendances', function (Blueprint $table) {
            $table->index(['attendance_date', 'status'], 'idx_teacher_attendance_date_status');
            $table->index(['teacher_id', 'attendance_date', 'status'], 'idx_teacher_full_attendance');
        });

        Schema::table('student_attendances', function (Blueprint $table) {
            $table->index(['attendance_date', 'status'], 'idx_student_attendance_date_status');
            $table->index(['student_id', 'attendance_date'], 'idx_student_attendance_lookup');
        });

        Schema::table('class_schedules', function (Blueprint $table) {
            $table->index(['day_of_week', 'effective_date'], 'idx_schedule_day_date');
        });

        Schema::table('payroll_records', function (Blueprint $table) {
            $table->index(['period_start', 'period_end', 'status'], 'idx_payroll_period_status');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('teacher_attendances', function (Blueprint $table) {
            $table->dropIndex('idx_teacher_attendance_date_status');
            $table->dropIndex('idx_teacher_full_attendance');
        });

        Schema::table('student_attendances', function (Blueprint $table) {
            $table->dropIndex('idx_student_attendance_date_status');
            $table->dropIndex('idx_student_attendance_lookup');
        });

        Schema::table('class_schedules', function (Blueprint $table) {
            $table->dropIndex('idx_schedule_day_date');
        });

        Schema::table('payroll_records', function (Blueprint $table) {
            $table->dropIndex('idx_payroll_period_status');
        });
    }
};
