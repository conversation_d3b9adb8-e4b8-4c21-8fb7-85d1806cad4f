<?php

namespace App\Enums;

enum BloodType: string
{
    case A = 'A';
    case B = 'B';
    case AB = 'AB';
    case O = 'O';

    public function label(): string
    {
        return match($this) {
            self::A => 'A',
            self::B => 'B',
            self::AB => 'AB',
            self::O => 'O',
        };
    }

    public static function options(): array
    {
        return [
            self::A->value => self::A->label(),
            self::B->value => self::B->label(),
            self::AB->value => self::AB->label(),
            self::O->value => self::O->label(),
        ];
    }
}
