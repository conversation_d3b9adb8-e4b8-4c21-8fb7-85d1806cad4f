<?php

namespace App\Enums;

enum EmploymentStatus: string
{
    case PERMANENT = 'permanent';
    case CONTRACT = 'contract';
    case TEMPORARY = 'temporary';
    case PROBATION = 'probation';

    public function label(): string
    {
        return match($this) {
            self::PERMANENT => 'Tetap',
            self::CONTRACT => 'Kontrak',
            self::TEMPORARY => 'Sementara',
            self::PROBATION => 'Masa <PERSON>',
        };
    }

    public static function options(): array
    {
        return [
            self::PERMANENT->value => self::PERMANENT->label(),
            self::CONTRACT->value => self::CONTRACT->label(),
            self::TEMPORARY->value => self::TEMPORARY->label(),
            self::PROBATION->value => self::PROBATION->label(),
        ];
    }
}
