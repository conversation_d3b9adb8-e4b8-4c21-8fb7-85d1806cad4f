<?php

namespace App\Enums;

enum Semester: string
{
    case ODD = 'odd';
    case EVEN = 'even';

    public function label(): string
    {
        return match($this) {
            self::ODD => 'Ganjil',
            self::EVEN => 'Genap',
        };
    }

    public static function options(): array
    {
        return [
            self::ODD->value => self::ODD->label(),
            self::EVEN->value => self::EVEN->label(),
        ];
    }
}
