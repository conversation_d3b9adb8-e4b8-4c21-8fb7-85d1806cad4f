# Business Logic Documentation for `db_rawooh_v2`

This document outlines the business logic for the `db_rawooh_v2` database, covering key modules and their associated features. Each module includes a description, business rules, validations, and workflows for CRUD operations, ensuring consistent implementation across the application. The logic is designed to align with the database schema and support educational institution management, including user management, academic scheduling, attendance tracking, and payroll processing.

---

## Table of Contents
1. [General Principles](#general-principles)
2. [Module: User Management](#module-user-management)
3. [Module: Teacher Management](#module-teacher-management)
4. [Module: Student Management](#module-student-management)
5. [Module: Parent Management](#module-parent-management)
6. [Module: Academic Year Management](#module-academic-year-management)
7. [Module: Program Management](#module-program-management)
8. [Module: Shift Management](#module-shift-management)
9. [Module: Classroom Management](#module-classroom-management)
10. [Module: Subject Management](#module-subject-management)
11. [Module: Lesson Hour Management](#module-lesson-hour-management)
12. [Module: Teacher Assignment Management](#module-teacher-assignment-management)
13. [Module: Class Schedule Management](#module-class-schedule-management)
14. [Module: Schedule Exception Management](#module-schedule-exception-management)
15. [Module: Geofence Management](#module-geofence-management)
16. [Module: Teacher Attendance Management](#module-teacher-attendance-management)
17. [Module: Student Attendance Management](#module-student-attendance-management)
18. [Module: Geofence Log Management](#module-geofence-log-management)
19. [Module: Teacher Salary Component Management](#module-teacher-salary-component-management)
20. [Module: Payroll Record Management](#module-payroll-record-management)

---

## General Principles
- **Data Integrity**: All operations must enforce foreign key constraints and unique indexes as defined in the database schema.
- **Validation**: Use request classes to validate input data, including enum values, unique constraints, and required fields.
- **Authorization**: Implement role-based access control using Spatie permissions to restrict actions based on user roles (e.g., admin, teacher, parent).
- **Transactions**: Use database transactions for operations involving multiple tables to ensure atomicity.
- **Error Handling**: Return standardized JSON responses with `message`, `data`, and `error` fields, including appropriate HTTP status codes.
- **Enum Usage**: Leverage PHP enums for fields with fixed values (e.g., `status`, `gender`) to ensure type safety and consistency.
- **Audit Logging**: Log significant actions (e.g., create, update, delete) for sensitive data using Laravel's event system or a custom audit log table.
- **Soft Deletes**: Consider implementing soft deletes for critical entities (e.g., `users`, `teachers`, `students`) to prevent accidental data loss.
- **Time Zone**: All date and time operations should use the application's configured time zone (e.g., WIB for Indonesia).

---

## Module: User Management

### Description
Manages user accounts for the system, including authentication and role assignment.

### Business Rules
- Users can have one of three roles: `admin`, `teacher`, or `student` (via Spatie permissions).
- A user can be associated with only one `teacher`, `student`, or `parent` record.
- Email and username must be unique across all users.
- Passwords must be hashed before storage.
- User status (`active`/`inactive`) determines login eligibility.
- Last login timestamp is updated on successful authentication.

### CRUD Operations
- **Create**:
  - Validate: `username` (unique), `email` (unique, valid email), `password` (min 8 characters, confirmed), `phone_number` (optional, max 20), `status` (boolean).
  - Logic: Hash password, assign default role (e.g., `user`), set `email_verified_at` to null unless verified.
  - Service: `CreateUserService` handles password hashing and role assignment.
- **Read**:
  - Logic: Retrieve paginated users with optional relations (`teacher`, `student`, `parent`). Filter by role or status if needed.
  - Authorization: Admins can view all users; teachers/students can view their own profile.
- **Update**:
  - Validate: Same as create, but exclude unique checks for unchanged `username`/`email`. Password is optional.
  - Logic: Update fields, rehash password if provided, update roles if authorized.
  - Service: `UpdateUserService` handles conditional password updates.
- **Delete**:
  - Logic: Prevent deletion if user is associated with a `teacher`, `student`, or `parent` record. Use soft delete if implemented.
  - Authorization: Only admins can delete users.

### Validations
- Use `StoreUserRequest` and `UpdateUserRequest` for input validation.
- Ensure `status` is boolean (1 for active, 0 for inactive).

---

## Module: Teacher Management

### Description
Manages teacher profiles linked to user accounts.

### Business Rules
- A teacher must be linked to a unique `user_id`.
- NIP (Nomor Induk Pegawai) is optional but must be unique if provided.
- GPS consent is required for geofence-based attendance tracking.
- Teachers can be assigned as homeroom teachers or subject teachers via `teacher_assignments`.

### CRUD Operations
- **Create**:
  - Validate: `user_id` (exists, unique in `teachers`), `nip` (optional, unique), `birth_place`, `birth_date`, `gender` (enum), `phone_number`, `full_address`, `gps_consent` (boolean).
  - Logic: Check if `user_id` is not already associated with a teacher. Create teacher record within a transaction.
  - Service: `CreateTeacherService` ensures user association validity.
- **Read**:
  - Logic: Retrieve paginated teachers with `user` relation. Filter by `nip` or `gender` if needed.
  - Authorization: Admins can view all teachers; teachers can view their own profile.
- **Update**:
  - Validate: Same as create, but exclude unique checks for unchanged `nip`.
  - Logic: Update fields, ensure `user_id` remains unchanged.
  - Service: `UpdateTeacherService` handles updates.
- **Delete**:
  - Logic: Prevent deletion if teacher is assigned to classrooms or has attendance records. Use soft delete if implemented.
  - Authorization: Only admins can delete teachers.

### Validations
- Use `StoreTeacherRequest` and `UpdateTeacherRequest`.
- Ensure `gender` is a valid `Gender` enum value.

---

## Module: Student Management

### Description
Manages student profiles linked to user accounts.

### Business Rules
- A student must be linked to a unique `user_id`.
- NIS and NISN must be unique.
- Students can be assigned to classrooms via `classroom_students`.
- Students can have multiple parents via `student_parents`.

### CRUD Operations
- **Create**:
  - Validate: `user_id` (exists, unique in `students`), `nis` (unique), `nisn` (unique), `birth_place`, `birth_date`, `gender` (enum), `religion`, `address`, `entry_year`, `phone` (optional).
  - Logic: Check if `user_id` is not already associated with a student. Create student record within a transaction.
  - Service: `CreateStudentService` ensures user association validity.
- **Read**:
  - Logic: Retrieve paginated students with `user` and `studentParents` relations. Filter by `nis`, `nisn`, or `entry_year`.
  - Authorization: Admins can view all students; students/parents can view their own profile.
- **Update**:
  - Validate: Same as create, but exclude unique checks for unchanged `nis`/`nisn`.
  - Logic: Update fields, ensure `user_id` remains unchanged.
  - Service: `UpdateStudentService` handles updates.
- **Delete**:
  - Logic: Prevent deletion if student is assigned to classrooms or has attendance records. Use soft delete if implemented.
  - Authorization: Only admins can delete students.

### Validations
- Use `StoreStudentRequest` and `UpdateStudentRequest`.
- Ensure `gender` is a valid `Gender` enum value.

---

## Module: Parent Management

### Description
Manages parent profiles, optionally linked to user accounts.

### Business Rules
- A parent can be linked to a `user_id` (optional).
- Parents are associated with students via `student_parents` with a `relation_type` (e.g., father, mother).
- Phone number is required for communication.

### CRUD Operations
- **Create**:
  - Validate: `user_id` (optional, exists), `name`, `phone_number`, `occupation` (optional), `address` (optional).
  - Logic: Create parent record, optionally link to a user.
- **Read**:
  - Logic: Retrieve paginated parents with `user` and `studentParents` relations. Filter by `phone_number` or `name`.
  - Authorization: Admins can view all parents; parents can view their own profile.
- **Update**:
  - Validate: Same as create, but allow `user_id` to be nullified or changed.
  - Logic: Update fields, ensure `user_id` is valid if provided.
- **Delete**:
  - Logic: Prevent deletion if parent is linked to students via `student_parents`. Use soft delete if implemented.
  - Authorization: Only admins can delete parents.

### Validations
- Use `StoreParentRequest` and `UpdateParentRequest`.

---

## Module: Academic Year Management

### Description
Manages academic years, including semesters and status.

### Business Rules
- Academic years have a unique `name` and a `semester` (`odd` or `even`).
- Start and end dates define the active period.
- Status can be `planned`, `active`, or `completed`.
- Only one academic year can be `active` at a time.

### CRUD Operations
- **Create**:
  - Validate: `name` (unique), `semester` (enum), `start_date`, `end_date` (after `start_date`), `status` (enum).
  - Logic: Check for overlapping active academic years. Set `status` to `planned` by default.
  - Service: `CreateAcademicYearService` enforces no overlapping active years.
- **Read**:
  - Logic: Retrieve paginated academic years. Filter by `status` or `semester`.
- **Update**:
  - Validate: Same as create, but exclude unique checks for unchanged `name`. Ensure `status` transitions are valid (e.g., `planned` to `active`, not `completed` to `planned`).
  - Logic: Update fields, check for overlapping active years if `status` changes to `active`.
  - Service: `UpdateAcademicYearService` enforces status transition and overlap rules.
- **Delete**:
  - Logic: Prevent deletion if academic year is linked to classrooms or schedules. Use soft delete if implemented.
  - Authorization: Only admins can delete academic years.

### Validations
- Use `StoreAcademicYearRequest` and `UpdateAcademicYearRequest`.
- Ensure `semester` is a valid `Semester` enum and `status` is a valid `AcademicYearStatus` enum.

---

## Module: Program Management

### Description
Manages academic programs (e.g., science, social studies).

### Business Rules
- Programs have a unique `name` and `code` (unique).
- Status can be `active` or `inactive`.
- Programs are linked to classrooms and subjects.

### CRUD Operations
- **Create**:
  - Validate: `name` (string), `code` (unique), `description` (optional), `status` (enum).
  - Logic: Set default `status` to `active`.
- **Read**:
  - Logic: Retrieve paginated programs. Filter by `status` or `code`.
- **Update**:
  - Validate: Same as create, but exclude unique checks for unchanged `code`.
- **Update**:
  - Logic: Update fields, ensure `status` is valid.
- **Delete**:
  - Logic: Prevent deletion if program is linked to classrooms or subjects. Use soft delete if implemented.
  - Authorization: Only admins can delete programs.

### Validations
- Use `StoreProgramRequest` and `UpdateProgramRequest`.
- Ensure `status` is a valid `ProgramStatus` enum.

---

## Module: Shift Management

### Description
Manages class shifts (e.g., morning, afternoon).

### Business Rules
- Shifts have a unique `name`.
- Status can be `active` or `inactive`.
- Shifts are linked to classrooms.

### CRUD Operations
- **Create**:
  - Validate: `name` (string), `description` (optional), `status` (enum).
  - Logic: Set default `status` to `active`.
- **Read**:
  - Logic: Retrieve paginated shifts. Filter by `status`.
- **Update**:
  - Validate: Same as create.
  - Logic: Update fields, ensure `status` is valid.
- **Delete**:
  - Logic: Prevent deletion if shift is linked to classrooms. Use soft delete if implemented.
  - Authorization: Only admins can delete shifts.

### Validations
- Use `StoreShiftRequest` and `UpdateShiftRequest`.
- Ensure `status` is a valid `ShiftStatus` enum.

---

## Module: Classroom Management

### Description
Manages classrooms, including their level and assignments.

### Business Rules
- Classrooms are linked to a `program`, `shift` (optional), `teacher` (optional), and `academic_year`.
- Level is one of `7`, `8`, or `9`.
- Status is `active` or `inactive`.
- Capacity must be positive.

### CRUD Operations
- **Create**:
  - Validate: `name`, `level` (enum), `capacity` (integer, min 1), `program_id` (exists), `shift_id` (optional, exists), `teacher_id` (optional, exists), `academic_year_id` (exists), `status` (enum).
  - Logic: Ensure `academic_year` is active or planned. Set default `status` to `active`.
  - Service: `CreateClassroomService` validates relations.
- **Read**:
  - Logic: Retrieve paginated classrooms with relations (`program`, `shift`, `teacher`, `academicYear`). Filter by `level` or `status`.
- **Update**:
  - Validate: Same as create.
  - Logic: Update fields, ensure relations remain valid.
  - Service: `UpdateClassroomService` handles updates.
- **Delete**:
  - Logic: Prevent deletion if classroom has students or teacher assignments. Use soft delete if implemented.
  - Authorization: Only admins can delete classrooms.

### Validations
- Use `StoreClassroomRequest` and `UpdateClassroomRequest`.
- Ensure `level` is a valid `ClassroomLevel` enum and `status` is a valid `ClassroomStatus` enum.

---

## Module: Subject Management

### Description
Manages subjects offered within programs.

### Business Rules
- Subjects are linked to a `program`.
- Subject names are unique within a program.

### CRUD Operations
- **Create**:
  - Validate: `name`, `program_id` (exists).
  - Logic: Ensure `program` is active.
- **Read**:
  - Logic: Retrieve paginated subjects with `program` relation. Filter by `program_id`.
- **Update**:
  - Validate: Same as create.
  - Logic: Update fields, ensure `program` remains valid.
- **Delete**:
  - Logic: Prevent deletion if subject is linked to teacher assignments. Use soft delete if implemented.
  - Authorization: Only admins can delete subjects.

### Validations
- Use `StoreSubjectRequest` and `UpdateSubjectRequest`.

---

## Module: Lesson Hour Management

### Description
Manages lesson hours defining class time slots.

### Business Rules
- Lesson hours have a unique combination of `start_time` and `end_time`.
- Sequence determines the order of lesson hours in a day.
- End time must be after start time.

### CRUD Operations
- **Create**:
  - Validate: `name`, `start_time` (time), `end_time` (time, after `start_time`), `sequence` (integer).
  - Logic: Ensure no overlapping time slots.
- **Read**:
  - Logic: Retrieve paginated lesson hours, sorted by `sequence`.
- **Update**:
  - Validate: Same as create.
  - Logic: Update fields, ensure no overlapping time slots.
- **Delete**:
  - Logic: Prevent deletion if lesson hour is linked to class schedules. Use soft delete if implemented.
  - Authorization: Only admins can delete lesson hours.

### Validations
- Use `StoreLessonHourRequest` and `UpdateLessonHourRequest`.

---

## Module: Teacher Assignment Management

### Description
Manages teacher assignments to subjects and classrooms.

### Business Rules
- Assignments are unique per `teacher`, `subject`, `classroom`, and `academic_year`.
- A teacher can be a homeroom teacher for only one classroom per academic year.
- Assignments are linked to active classrooms and subjects.

### CRUD Operations
- **Create**:
  - Validate: `teacher_id` (exists), `subject_id` (exists), `classroom_id` (exists), `academic_year_id` (exists), `is_homeroom_teacher` (boolean).
  - Logic: Check for unique assignment and homeroom teacher constraints. Ensure `academic_year` is active or planned.
  - Service: `CreateTeacherAssignmentService` enforces constraints.
- **Read**:
  - Logic: Retrieve paginated assignments with relations (`teacher`, `subject`, `classroom`, `academicYear`).
- **Update**:
  - Validate: Same as create.
  - Logic: Update fields, recheck constraints.
  - Service: `UpdateTeacherAssignmentService` handles updates.
- **Delete**:
  - Logic: Prevent deletion if assignment is linked to class schedules. Use soft delete if implemented.
  - Authorization: Only admins can delete assignments.

### Validations
- Use `StoreTeacherAssignmentRequest` and `UpdateTeacherAssignmentRequest`.

---

## Module: Class Schedule Management

### Description
Manages class schedules for teacher assignments.

### Business Rules
- Schedules are unique per `teacher_assignment`, `lesson_hour`, `day_of_week`, and `academic_year`.
- Schedules are linked to active academic years.
- Day of week is one of `monday` to `sunday`.

### CRUD Operations
- **Create**:
  - Validate: `teacher_assignment_id` (exists), `lesson_hour_id` (exists), `day_of_week` (enum), `academic_year_id` (exists).
  - Logic: Check for unique schedule and ensure no time conflicts for the teacher.
  - Service: `CreateClassScheduleService` validates conflicts.
- **Read**:
  - Logic: Retrieve paginated schedules with relations (`teacherAssignment`, `lessonHour`, `academicYear`). Filter by `day_of_week`.
- **Update**:
  - Validate: Same as create.
  - Logic: Update fields, recheck conflicts.
  - Service: `UpdateClassScheduleService` handles updates.
- **Delete**:
  - Logic: Prevent deletion if schedule has attendance records. Use soft delete if implemented.
  - Authorization: Only admins can delete schedules.

### Validations
- Use `StoreClassScheduleRequest` and `UpdateClassScheduleRequest`.
- Ensure `day_of_week` is a valid `DayOfWeek` enum.

---

## Module: Schedule Exception Management

### Description
Manages exceptions to class schedules (e.g., cancellations).

### Business Rules
- Exceptions are unique per `class_schedule` and `exception_date`.
- Reason is required to document the exception.

### CRUD Operations
- **Create**:
  - Validate: `class_schedule_id` (exists), `exception_date` (date), `reason`.
  - Logic: Ensure `exception_date` is within the academic year of the schedule.
- **Read**:
  - Logic: Retrieve paginated exceptions with `classSchedule` relation. Filter by `exception_date`.
- **Update**:
  - Validate: Same as create.
  - Logic: Update fields, recheck date validity.
- **Delete**:
  - Logic: Allow deletion unless exception is referenced elsewhere. Use soft delete if implemented.
  - Authorization: Only admins can delete exceptions.

### Validations
- Use `StoreScheduleExceptionRequest` and `UpdateScheduleExceptionRequest`.

---

## Module: Geofence Management

### Description
Manages geofence areas for attendance tracking.

### Business Rules
- Geofences define a circular area with a `latitude`, `longitude`, and `radius` (in meters).
- Name is required for identification.
- Latitude and longitude must be valid coordinates.

### CRUD Operations
- **Create**:
  - Validate: `name`, `latitude` (between -90 and 90), `longitude` (between -180 and 180), `radius` (positive).
  - Logic: Create geofence record.
- **Read**:
  - Logic: Retrieve paginated geofences.
- **Update**:
  - Validate: Same as create.
  - Logic: Update fields, ensure coordinates remain valid.
- **Delete**:
  - Logic: Prevent deletion if geofence is linked to attendance or logs. Use soft delete if implemented.
  - Authorization: Only admins can delete geofences.

### Validations
- Use `StoreGeofenceRequest` and `UpdateGeofenceRequest`.

---

## Module: Teacher Attendance Management

### Description
Manages teacher attendance records with geofence validation.

### Business Rules
- Attendance is unique per `teacher`, `class_schedule`, and `attendance_date`.
- Status is one of `present`, `absent`, `late`, or `substitute`.
- Geofence validation is required if `geofence_id` is provided and teacher has given GPS consent.
- Check-out time must be after check-in time.
- Substitute teachers must specify an `original_teacher_id`.

### CRUD Operations
- **Create**:
  - Validate: `teacher_id` (exists), `class_schedule_id` (exists), `attendance_date` (date), `check_in_time` (datetime), `check_out_time` (optional, after `check_in_time`), `latitude`, `longitude`, `geofence_id` (optional, exists), `status` (enum), `is_substitute_teacher` (boolean), `original_teacher_id` (optional, exists), `notes` (optional).
  - Logic: Validate geofence distance using Haversine formula if `geofence_id` is provided. Ensure teacher has GPS consent. Check for unique attendance record. Use transaction for creation.
  - Service: `CreateTeacherAttendanceService` handles geofence validation and uniqueness.
- **Read**:
  - Logic: Retrieve paginated attendances with relations (`teacher`, `classSchedule`, `geofence`, `originalTeacher`). Filter by `status` or `attendance_date`.
- **Update**:
  - Validate: Same as create, but optional fields.
  - Logic: Revalidate geofence if coordinates or `geofence_id` change. Update fields within a transaction.
  - Service: `UpdateTeacherAttendanceService` handles updates.
- **Delete**:
  - Logic: Prevent deletion if attendance has student attendance records. Use soft delete if implemented.
  - Authorization: Only admins can delete attendances.

### Validations
- Use `StoreTeacherAttendanceRequest` and `UpdateTeacherAttendanceRequest`.
- Ensure `status` is a valid `TeacherAttendanceStatus` enum.

---

## Module: Student Attendance Management

### Description
Manages student attendance records linked to teacher attendance.

### Business Rules
- Attendance is unique per `student` and `teacher_attendance`.
- Status is one of `present`, `absent`, `sick`, `permission`, or `late`.
- Notes are optional for documenting reasons (e.g., permission details).

### CRUD Operations
- **Create**:
  - Validate: `student_id` (exists), `teacher_attendance_id` (exists), `attendance_status` (enum), `notes` (optional).
  - Logic: Ensure `teacher_attendance` is valid and not deleted. Check for unique attendance.
- **Read**:
  - Logic: Retrieve paginated attendances with relations (`student`, `teacherAttendance`). Filter by `attendance_status` or date via `teacherAttendance`.
- **Update**:
  - Validate: Same as create, but optional fields.
  - Logic: Update fields, recheck uniqueness.
- **Delete**:
  - Logic: Allow deletion unless referenced elsewhere. Use soft delete if implemented.
  - Authorization: Only admins or teachers can delete student attendances.

### Validations
- Use `StoreStudentAttendanceRequest` and `UpdateStudentAttendanceRequest`.
- Ensure `attendance_status` is a valid `StudentAttendanceStatus` enum.

---

## Module: Geofence Log Management

### Description
Logs teacher entry/exit events for geofence areas.

### Business Rules
- Logs are created for `enter` or `exit` actions.
- Logs include `teacher_id`, `geofence_id`, `latitude`, `longitude`, and `logged_at` timestamp.
- Latitude and longitude must be valid coordinates.

### CRUD Operations
- **Create**:
  - Validate: `teacher_id` (exists), `geofence_id` (exists), `action` (enum), `latitude`, `longitude`, `logged_at` (datetime).
  - Logic: Create log record, ensure teacher has GPS consent.
- **Read**:
  - Logic: Retrieve paginated logs with relations (`teacher`, `geofence`). Filter by `action` or `logged_at`.
- **Update**:
  - Validate: Same as create, but optional fields.
  - Logic: Update fields, recheck validity.
- **Delete**:
  - Logic: Allow deletion unless referenced elsewhere. Use soft delete if implemented.
  - Authorization: Only admins can delete logs.

### Validations
- Use `StoreGeofenceLogRequest` and `UpdateGeofenceLogRequest`.
- Ensure `action` is a valid `GeofenceAction` enum.

---

## Module: Teacher Salary Component Management

### Description
Manages salary components for teachers.

### Business Rules
- Components are linked to a `teacher`.
- Calculation type is one of `per_hour`, `fixed`, or `percentage`.
- Effective date is required; end date is optional.
- Amount must be non-negative.

### CRUD Operations
- **Create**:
  - Validate: `teacher_id` (exists), `component_name`, `amount` (numeric, min 0), `calculation_type` (enum), `effective_date` (date), `end_date` (optional, after `effective_date`).
  - Logic: Create component record.
- **Read**:
  - Logic: Retrieve paginated components with `teacher` relation. Filter by `calculation_type` or `effective_date`.
- **Update**:
  - Validate: Same as create, but optional fields.
  - Logic: Update fields, recheck date validity.
- **Delete**:
  - Logic: Allow deletion unless referenced in payroll calculations. Use soft delete if implemented.
  - Authorization: Only admins can delete components.

### Validations
- Use `StoreTeacherSalaryComponentRequest` and `UpdateTeacherSalaryComponentRequest`.
- Ensure `calculation_type` is a valid `CalculationType` enum.

---

## Module: Payroll Record Management

### Description
Manages payroll records for teachers.

### Business Rules
- Records are unique per `teacher`, `period_name`, and `academic_year`.
- Status is one of `generated`, `paid`, or `pending`.
- Total regular and substitute teaching hours are calculated based on `teacher_attendances`.
- Generated by user and timestamps track payroll processing.

### CRUD Operations
- **Create**:
  - Validate: `teacher_id` (exists), `academic_year_id` (exists), `period_name`, `start_date` (date), `end_date` (date, after `start_date`), `total_regular_teaching_hours` (numeric, min 0), `total_substitute_teaching_hours` (numeric, min 0), `status` (enum), `generated_by_user_id` (optional, exists), `generated_at` (optional, datetime), `paid_at` (optional, datetime).
  - Logic: Calculate teaching hours based on `teacher_attendances` within the period. Ensure no overlapping periods for the teacher. Use transaction for creation.
  - Service: `CreatePayrollRecordService` handles hour calculations and uniqueness.
- **Read**:
  - Logic: Retrieve paginated records with relations (`teacher`, `academicYear`, `generatedBy`). Filter by `status` or period.
- **Update**:
  - Validate: Same as create, but optional fields.
  - Logic: Recalculate hours if period changes. Update fields within a transaction.
  - Service: `UpdatePayrollRecordService` handles updates.
- **Delete**:
  - Logic: Prevent deletion if payroll is `paid`. Use soft delete if implemented.
  - Authorization: Only admins can delete payroll records.

### Validations
- Use `StorePayrollRecordRequest` and `UpdatePayrollRecordRequest`.
- Ensure `status` is a valid `PayrollStatus` enum.

---

## Implementation Notes
- **Services**: Use services for complex logic (e.g., geofence validation, teaching hour calculations) to keep controllers clean and reusable.
- **Authorization**: Implement Spatie permissions to define roles (e.g., `admin`, `teacher`, `student`, `parent`) and permissions for each action.
- **API Responses**:
  - Success: `{ "message": "Operation successful", "data": {...} }` with status 200/201.
  - Error: `{ "message": "Operation failed", "error": "Error message" }` with status 400/422/500.
- **Testing**: Write unit and feature tests for each module to verify business rules, validations, and workflows.
- **Documentation**: Use OpenAPI/Swagger to document API endpoints, including request/response schemas and enum values.
- **Scalability**: Design services and controllers to be extensible for additional features (e.g., notifications, reports).
- **Localization**: Consider supporting multiple languages for user-facing messages and validations.

This documentation provides a clear and consistent framework for implementing business logic across all modules in the `db_rawooh_v2` application, ensuring robust and maintainable code.