# PHP Enums and Model Integration for `db_rawooh_v2`

This artifact provides PHP enum classes for the enumerated fields in the `db_rawooh_v2` database and demonstrates how to integrate them into the Laravel models. The enums will cover the following fields:
- `academic_years.semester` (`odd`, `even`)
- `academic_years.status` (`planned`, `active`, `completed`)
- `programs.status` (`active`, `inactive`)
- `shifts.status` (`active`, `inactive`)
- `classrooms.level` (`7`, `8`, `9`)
- `classrooms.status` (`active`, `inactive`)
- `teachers.gender` (`male`, `female`)
- `students.gender` (`male`, `female`)
- `class_schedules.day_of_week` (`monday`, `tuesday`, `wednesday`, `thursday`, `friday`, `saturday`, `sunday`)
- `teacher_attendances.status` (`present`, `absent`, `late`, `substitute`)
- `student_attendances.attendance_status` (`present`, `absent`, `sick`, `permission`, `late`)
- `teacher_salary_components.calculation_type` (`per_hour`, `fixed`, `percentage`)
- `payroll_records.status` (`generated`, `paid`, `pending`)
- `geofence_logs.action` (`enter`, `exit`)

Each enum will be implemented as a backed enum with string values to match the database schema. I'll also show how to update the models to use these enums and provide methods for easier interaction.

---

## Directory Structure
- Enums: `app/Enums`
- Models: `app/Models` (updated to use enums)
- Requests: `app/Http/Requests` (updated to validate enum values)

---

## Enum Classes

### 1. `Semester.php`
```php
<?php

namespace App\Enums;

enum Semester: string
{
    case Odd = 'odd';
    case Even = 'even';
}
```

### 2. `AcademicYearStatus.php`
```php
<?php

namespace App\Enums;

enum AcademicYearStatus: string
{
    case Planned = 'planned';
    case Active = 'active';
    case Completed = 'completed';
}
```

### 3. `ProgramStatus.php`
```php
<?php

namespace App\Enums;

enum ProgramStatus: string
{
    case Active = 'active';
    case Inactive = 'inactive';
}
```

### 4. `ShiftStatus.php`
```php
<?php

namespace App\Enums;

enum ShiftStatus: string
{
    case Active = 'active';
    case Inactive = 'inactive';
}
```

### 5. `ClassroomLevel.php`
```php
<?php

namespace App\Enums;

enum ClassroomLevel: string
{
    case Seven = '7';
    case Eight = '8';
    case Nine = '9';
}
```

### 6. `ClassroomStatus.php`
```php
<?php

namespace App\Enums;

enum ClassroomStatus: string
{
    case Active = 'active';
    case Inactive = 'inactive';
}
```

### 7. `Gender.php`
```php
<?php

namespace App\Enums;

enum Gender: string
{
    case Male = 'male';
    case Female = 'female';
}
```

### 8. `DayOfWeek.php`
```php
<?php

namespace App\Enums;

enum DayOfWeek: string
{
    case Monday = 'monday';
    case Tuesday = 'tuesday';
    case Wednesday = 'wednesday';
    case Thursday = 'thursday';
    case Friday = 'friday';
    case Saturday = 'saturday';
    case Sunday = 'sunday';
}
```

### 9. `TeacherAttendanceStatus.php`
```php
<?php

namespace App\Enums;

enum TeacherAttendanceStatus: string
{
    case Present = 'present';
    case Absent = 'absent';
    case Late = 'late';
    case Substitute = 'substitute';
}
```

### 10. `StudentAttendanceStatus.php`
```php
<?php

namespace App\Enums;

enum StudentAttendanceStatus: string
{
    case Present = 'present';
    case Absent = 'absent';
    case Sick = 'sick';
    case Permission = 'permission';
    case Late = 'late';
}
```

### 11. `CalculationType.php`
```php
<?php

namespace App\Enums;

enum CalculationType: string
{
    case PerHour = 'per_hour';
    case Fixed = 'fixed';
    case Percentage = 'percentage';
}
```

### 12. `PayrollStatus.php`
```php
<?php

namespace App\Enums;

enum PayrollStatus: string
{
    case Generated = 'generated';
    case Paid = 'paid';
    case Pending = 'pending';
}
```

### 13. `GeofenceAction.php`
```php
<?php

namespace App\Enums;

enum GeofenceAction: string
{
    case Enter = 'enter';
    case Exit = 'exit';
}
```

---

## Updated Models

Below are the updated models that use the enums. Only the affected models are shown with their modified `casts` properties and any additional methods for enum handling. Other models remain unchanged from the previous artifact.

### 1. `AcademicYear.php`
```php
<?php

namespace App\Models;

use App\Enums\Semester;
use App\Enums\AcademicYearStatus;
use Illuminate\Database\Eloquent\Model;

class AcademicYear extends Model
{
    protected $fillable = [
        'name', 'semester', 'start_date', 'end_date', 'status',
    ];

    protected $casts = [
        'semester' => Semester::class,
        'status' => AcademicYearStatus::class,
        'start_date' => 'date',
        'end_date' => 'date',
    ];

    public function classrooms()
    {
        return $this->hasMany(Classroom::class);
    }

    public function classroomStudents()
    {
        return $this->hasMany(ClassroomStudent::class);
    }

    public function teacherAssignments()
    {
        return $this->hasMany(TeacherAssignment::class);
    }

    public function classSchedules()
    {
        return $this->hasMany(ClassSchedule::class);
    }

    public function payrollRecords()
    {
        return $this->hasMany(PayrollRecord::class);
    }
}
```

### 2. `Program.php`
```php
<?php

namespace App\Models;

use App\Enums\ProgramStatus;
use Illuminate\Database\Eloquent\Model;

class Program extends Model
{
    protected $fillable = [
        'name', 'code', 'description', 'status',
    ];

    protected $casts = [
        'status' => ProgramStatus::class,
    ];

    public function classrooms()
    {
        return $this->hasMany(Classroom::class);
    }

    public function subjects()
    {
        return $this->hasMany(Subject::class);
    }
}
```

### 3. `Shift.php`
```php
<?php

namespace App\Models;

use App\Enums\ShiftStatus;
use Illuminate\Database\Eloquent\Model;

class Shift extends Model
{
    protected $fillable = [
        'name', 'description', 'status',
    ];

    protected $casts = [
        'status' => ShiftStatus::class,
    ];

    public function classrooms()
    {
        return $this->hasMany(Classroom::class);
    }
}
```

### 4. `Classroom.php`
```php
<?php

namespace App\Models;

use App\Enums\ClassroomLevel;
use App\Enums\ClassroomStatus;
use Illuminate\Database\Eloquent\Model;

class Classroom extends Model
{
    protected $fillable = [
        'name', 'level', 'capacity', 'program_id', 'shift_id', 'teacher_id', 'academic_year_id', 'status', 'description',
    ];

    protected $casts = [
        'level' => ClassroomLevel::class,
        'status' => ClassroomStatus::class,
    ];

    public function program()
    {
        return $this->belongsTo(Program::class);
    }

    public function shift()
    {
        return $this->belongsTo(Shift::class);
    }

    public function teacher()
    {
        return $this->belongsTo(Teacher::class);
    }

    public function academicYear()
    {
        return $this->belongsTo(AcademicYear::class);
    }

    public function classroomStudents()
    {
        return $this->hasMany(ClassroomStudent::class);
    }

    public function teacherAssignments()
    {
        return $this->hasMany(TeacherAssignment::class);
    }
}
```

### 5. `Teacher.php`
```php
<?php

namespace App\Models;

use App\Enums\Gender;
use Illuminate\Database\Eloquent\Model;

class Teacher extends Model
{
    protected $fillable = [
        'user_id', 'nip', 'birth_place', 'birth_date', 'gender', 'phone_number', 'full_address', 'gps_consent', 'gps_consent_given_at',
    ];

    protected $casts = [
        'gender' => Gender::class,
        'gps_consent' => 'boolean',
        'birth_date' => 'date',
        'gps_consent_given_at' => 'datetime',
    ];

    public function user()
    {
        return $this->belongsTo(User::class);
    }

    public function classrooms()
    {
        return $this->hasMany(Classroom::class);
    }

    public function teacherAssignments()
    {
        return $this->hasMany(TeacherAssignment::class);
    }

    public function teacherAttendances()
    {
        return $this->hasMany(TeacherAttendance::class);
    }

    public function originalTeacherAttendances()
    {
        return $this->hasMany(TeacherAttendance::class, 'original_teacher_id');
    }

    public function geofenceLogs()
    {
        return $this->hasMany(GeofenceLog::class);
    }

    public function teacherSalaryComponents()
    {
        return $this->hasMany(TeacherSalaryComponent::class);
    }

    public function payrollRecords()
    {
        return $this->hasMany(PayrollRecord::class);
    }
}
```

### 6. `Student.php`
```php
<?php

namespace App\Models;

use App\Enums\Gender;
use Illuminate\Database\Eloquent\Model;

class Student extends Model
{
    protected $fillable = [
        'user_id', 'nis', 'nisn', 'birth_place', 'birth_date', 'gender', 'religion', 'address', 'entry_year', 'profile_picture', 'phone',
    ];

    protected $casts = [
        'gender' => Gender::class,
        'birth_date' => 'date',
        'entry_year' => 'integer',
    ];

    public function user()
    {
        return $this->belongsTo(User::class);
    }

    public function studentParents()
    {
        return $this->hasMany(StudentParent::class);
    }

    public function classroomStudents()
    {
        return $this->hasMany(ClassroomStudent::class);
    }

    public function studentAttendances()
    {
        return $this->hasMany(StudentAttendance::class);
    }
}
```

### 7. `ClassSchedule.php`
```php
<?php

namespace App\Models;

use App\Enums\DayOfWeek;
use Illuminate\Database\Eloquent\Model;

class ClassSchedule extends Model
{
    protected $fillable = [
        'teacher_assignment_id', 'lesson_hour_id', 'day_of_week', 'academic_year_id',
    ];

    protected $casts = [
        'day_of_week' => DayOfWeek::class,
    ];

    public function teacherAssignment()
    {
        return $this->belongsTo(TeacherAssignment::class);
    }

    public function lessonHour()
    {
        return $this->belongsTo(LessonHour::class);
    }

    public function academicYear()
    {
        return $this->belongsTo(AcademicYear::class);
    }

    public function scheduleExceptions()
    {
        return $this->hasMany(ScheduleException::class);
    }

    public function teacherAttendances()
    {
        return $this->hasMany(TeacherAttendance::class);
    }
}
```

### 8. `TeacherAttendance.php`
```php
<?php

namespace App\Models;

use App\Enums\TeacherAttendanceStatus;
use Illuminate\Database\Eloquent\Model;

class TeacherAttendance extends Model
{
    protected $fillable = [
        'teacher_id', 'class_schedule_id', 'attendance_date', 'check_in_time', 'check_out_time',
        'latitude', 'longitude', 'geofence_id', 'status', 'is_substitute_teacher', 'original_teacher_id', 'notes',
    ];

    protected $casts = [
        'attendance_date' => 'date',
        'check_in_time' => 'datetime',
        'check_out_time' => 'datetime',
        'latitude' => 'decimal:8',
        'longitude' => 'decimal:8',
        'status' => TeacherAttendanceStatus::class,
        'is_substitute_teacher' => 'boolean',
    ];

    public function teacher()
    {
        return $this->belongsTo(Teacher::class);
    }

    public function classSchedule()
    {
        return $this->belongsTo(ClassSchedule::class);
    }

    public function geofence()
    {
        return $this->belongsTo(Geofence::class);
    }

    public function originalTeacher()
    {
        return $this->belongsTo(Teacher::class, 'original_teacher_id');
    }

    public function studentAttendances()
    {
        return $this->hasMany(StudentAttendance::class);
    }
}
```

### 9. `StudentAttendance.php`
```php
<?php

namespace App\Models;

use App\Enums\StudentAttendanceStatus;
use Illuminate\Database\Eloquent\Model;

class StudentAttendance extends Model
{
    protected $fillable = [
        'student_id', 'teacher_attendance_id', 'attendance_status', 'notes',
    ];

    protected $casts = [
        'attendance_status' => StudentAttendanceStatus::class,
    ];

    public function student()
    {
        return $this->belongsTo(Student::class);
    }

    public function teacherAttendance()
    {
        return $this->belongsTo(TeacherAttendance::class);
    }
}
```

### 10. `TeacherSalaryComponent.php`
```php
<?php

namespace App\Models;

use App\Enums\CalculationType;
use Illuminate\Database\Eloquent\Model;

class TeacherSalaryComponent extends Model
{
    protected $fillable = [
        'teacher_id', 'component_name', 'amount', 'calculation_type', 'effective_date', 'end_date',
    ];

    protected $casts = [
        'amount' => 'decimal:2',
        'calculation_type' => CalculationType::class,
        'effective_date' => 'date',
        'end_date' => 'date',
    ];

    public function teacher()
    {
        return $this->belongsTo(Teacher::class);
    }
}
```

### 11. `PayrollRecord.php`
```php
<?php

namespace App\Models;

use App\Enums\PayrollStatus;
use Illuminate\Database\Eloquent\Model;

class PayrollRecord extends Model
{
    protected $fillable = [
        'teacher_id', 'academic_year_id', 'period_name', 'start_date', 'end_date',
        'total_regular_teaching_hours', 'total_substitute_teaching_hours', 'status',
        'generated_by_user_id', 'generated_at', 'paid_at',
    ];

    protected $casts = [
        'start_date' => 'date',
        'end_date' => 'date',
        'total_regular_teaching_hours' => 'decimal:2',
        'total_substitute_teaching_hours' => 'decimal:2',
        'status' => PayrollStatus::class,
        'generated_at' => 'datetime',
        'paid_at' => 'datetime',
    ];

    public function teacher()
    {
        return $this->belongsTo(Teacher::class);
    }

    public function academicYear()
    {
        return $this->belongsTo(AcademicYear::class);
    }

    public function generatedBy()
    {
        return $this->belongsTo(User::class, 'generated_by_user_id');
    }
}
```

### 12. `GeofenceLog.php`
```php
<?php

namespace App\Models;

use App\Enums\GeofenceAction;
use Illuminate\Database\Eloquent\Model;

class GeofenceLog extends Model
{
    protected $fillable = [
        'teacher_id', 'geofence_id', 'action', 'latitude', 'longitude', 'logged_at',
    ];

    protected $casts = [
        'action' => GeofenceAction::class,
        'latitude' => 'decimal:8',
        'longitude' => 'decimal:8',
        'logged_at' => 'datetime',
    ];

    public function teacher()
    {
        return $this->belongsTo(Teacher::class);
    }

    public function geofence()
    {
        return $this->belongsTo(Geofence::class);
    }
}
```

---

## Updated Request Classes

To ensure the API accepts valid enum values, the request classes need to be updated to validate against the enum cases. Below are the modified request classes for the affected fields (only showing relevant changes).

### 1. `StoreAcademicYearRequest.php`
```php
<?php

namespace App\Http\Requests\AcademicYear;

use App\Enums\Semester;
use App\Enums\AcademicYearStatus;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rules\Enum;

class StoreAcademicYearRequest extends FormRequest
{
    public function authorize(): bool
    {
        return true; // Adjust based on authorization logic
    }

    public function rules(): array
    {
        return [
            'name' => ['required', 'string', 'max:255'],
            'semester' => ['required', new Enum(Semester::class)],
            'start_date' => ['required', 'date'],
            'end_date' => ['required', 'date', 'after:start_date'],
            'status' => ['required', new Enum(AcademicYearStatus::class)],
        ];
    }
}
```

### 2. `UpdateAcademicYearRequest.php`
```php
<?php

namespace App\Http\Requests\AcademicYear;

use App\Enums\Semester;
use App\Enums\AcademicYearStatus;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rules\Enum;

class UpdateAcademicYearRequest extends FormRequest
{
    public function authorize(): bool
    {
        return true; // Adjust based on authorization logic
    }

    public function rules(): array
    {
        return [
            'name' => ['sometimes', 'string', 'max:255'],
            'semester' => ['sometimes', new Enum(Semester::class)],
            'start_date' => ['sometimes', 'date'],
            'end_date' => ['sometimes', 'date', 'after:start_date'],
            'status' => ['sometimes', new Enum(AcademicYearStatus::class)],
        ];
    }
}
```

### 3. `StoreProgramRequest.php`
```php
<?php

namespace App\Http\Requests\Program;

use App\Enums\ProgramStatus;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rules\Enum;

class StoreProgramRequest extends FormRequest
{
    public function authorize(): bool
    {
        return true; // Adjust based on authorization logic
    }

    public function rules(): array
    {
        return [
            'name' => ['required', 'string', 'max:255'],
            'code' => ['required', 'string', 'max:255', 'unique:programs'],
            'description' => ['nullable', 'string'],
            'status' => ['required', new Enum(ProgramStatus::class)],
        ];
    }
}
```

### 4. `UpdateProgramRequest.php`
```php
<?php

namespace App\Http\Requests\Program;

use App\Enums\ProgramStatus;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rules\Enum;

class UpdateProgramRequest extends FormRequest
{
    public function authorize(): bool
    {
        return true; // Adjust based on authorization logic
    }

    public function rules(): array
    {
        return [
            'name' => ['sometimes', 'string', 'max:255'],
            'code' => ['sometimes', 'string', 'max:255', 'unique:programs,code,' . $this->program->id],
            'description' => ['nullable', 'string'],
            'status' => ['sometimes', new Enum(ProgramStatus::class)],
        ];
    }
}
```

### 5. `StoreShiftRequest.php`
```php
 "<?php

namespace App\Http\Requests\Shift;

use App\Enums\ShiftStatus;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rules\Enum;

class StoreShiftRequest extends FormRequest
{
    public function authorize(): bool
    {
        return true; // Adjust based on authorization logic
    }

    public function rules(): array
    {
        return [
            'name' => ['required', 'string', 'max:255'],
            'description' => ['nullable', 'string'],
            'status' => ['required', new Enum(ShiftStatus::class)],
        ];
    }
}
```

### 6. `UpdateShiftRequest.php`
```php
<?php

namespace App\Http\Requests\Shift;

use App\Enums\ShiftStatus;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rules\Enum;

class UpdateShiftRequest extends FormRequest
{
    public function authorize(): bool
    {
        return true; // Adjust based on authorization logic
    }

    public function rules(): array
    {
        return [
            'name' => ['sometimes', 'string', 'max:255'],
            'description' => ['nullable', 'string'],
            'status' => ['sometimes', new Enum(ShiftStatus::class)],
        ];
    }
}
```

### 7. `StoreClassroomRequest.php` (Updated)
```php
<?php

namespace App\Http\Requests\Classroom;

use App\Enums\ClassroomLevel;
use App\Enums\ClassroomStatus;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rules\Enum;

class StoreClassroomRequest extends FormRequest
{
    public function authorize(): bool
    {
        return true; // Adjust based on authorization logic
    }

    public function rules(): array
    {
        return [
            'name' => ['required', 'string', 'max:255'],
            'level' => ['required', new Enum(ClassroomLevel::class)],
            'capacity' => ['required', 'integer', 'min:1'],
            'program_id' => ['required', 'exists:programs,id'],
            'shift_id' => ['nullable', 'exists:shifts,id'],
            'teacher_id' => ['nullable', 'exists:teachers,id'],
            'academic_year_id' => ['required', 'exists:academic_years,id'],
            'status' => ['required', new Enum(ClassroomStatus::class)],
            'description' => ['nullable', 'string'],
        ];
    }
}
```

### 8. `UpdateClassroomRequest.php` (Updated)
```php
<?php

namespace App\Http\Requests\Classroom;

use App\Enums\ClassroomLevel;
use App\Enums\ClassroomStatus;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rules\Enum;

class UpdateClassroomRequest extends FormRequest
{
    public function authorize(): bool
    {
        return true; // Adjust based on authorization logic
    }

    public function rules(): array
    {
        return [
            'name' => ['sometimes', 'string', 'max:255'],
            'level' => ['sometimes', new Enum(ClassroomLevel::class)],
            'capacity' => ['sometimes', 'integer', 'min:1'],
            'program_id' => ['sometimes', 'exists:programs,id'],
            'shift_id' => ['nullable', 'exists:shifts,id'],
            'teacher_id' => ['nullable', 'exists:teachers,id'],
            'academic_year_id' => ['sometimes', 'exists:academic_years,id'],
            'status' => ['sometimes', new Enum(ClassroomStatus::class)],
            'description' => ['nullable', 'string'],
        ];
    }
}
```

### 9. `StoreTeacherRequest.php` (Updated)
```php
<?php

namespace App\Http\Requests\Teacher;

use App\Enums\Gender;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rules\Enum;

class StoreTeacherRequest extends FormRequest
{
    public function authorize(): bool
    {
        return true; // Adjust based on authorization logic
    }

    public function rules(): array
    {
        return [
            'user_id' => ['required', 'exists:users,id'],
            'nip' => ['nullable', 'string', 'max:255', 'unique:teachers'],
            'birth_place' => ['required', 'string', 'max:255'],
            'birth_date' => ['required', 'date'],
            'gender' => ['required', new Enum(Gender::class)],
            'phone_number' => ['required', 'string', 'max:20'],
            'full_address' => ['required', 'string'],
            'gps_consent' => ['required', 'boolean'],
            'gps_consent_given_at' => ['nullable', 'date'],
        ];
    }
}
```

### 10. `UpdateTeacherRequest.php` (Updated)
```php
<?php

namespace App\Http\Requests\Teacher;

use App\Enums\Gender;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rules\Enum;

class UpdateTeacherRequest extends FormRequest
{
    public function authorize(): bool
    {
        return true; // Adjust based on authorization logic
    }

    public function rules(): array
    {
        return [
            'nip' => ['sometimes', 'string', 'max:255', 'unique:teachers,nip,' . $this->teacher->id],
            'birth_place' => ['sometimes', 'string', 'max:255'],
            'birth_date' => ['sometimes', 'date'],
            'gender' => ['sometimes', new Enum(Gender::class)],
            'phone_number' => ['sometimes', 'string', 'max:20'],
            'full_address' => ['sometimes', 'string'],
            'gps_consent' => ['sometimes', 'boolean'],
            'gps_consent_given_at' => ['nullable', 'date'],
        ];
    }
}
```

### 11. `StoreStudentRequest.php` (Updated)
```php
<?php

namespace App\Http\Requests\Student;

use App\Enums\Gender;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rules\Enum;

class StoreStudentRequest extends FormRequest
{
    public function authorize(): bool
    {
        return true; // Adjust based on authorization logic
    }

    public function rules(): array
    {
        return [
            'user_id' => ['required', 'exists:users,id'],
            'nis' => ['required', 'string', 'max:20', 'unique:students'],
            'nisn' => ['required', 'string', 'max:20', 'unique:students'],
            'birth_place' => ['required', 'string', 'max:255'],
            'birth_date' => ['required', 'date'],
            'gender' => ['required', new Enum(Gender::class)],
            'religion' => ['required', 'string', 'max:20'],
            'address' => ['required', 'string'],
            'entry_year' => ['required', 'integer', 'min:1900', 'max:9999'],
            'profile_picture' => ['nullable', 'string', 'max:255'],
            'phone' => ['nullable', 'string', 'max:20'],
        ];
    }
}
```

### 12. `UpdateStudentRequest.php` (Updated)
```php
<?php

namespace App\Http\Requests\Student;

use App\Enums\Gender;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rules\Enum;

class UpdateStudentRequest extends FormRequest
{
    public function authorize(): bool
    {
        return true; // Adjust based on authorization logic
    }

    public function rules(): array
    {
        return [
            'nis' => ['sometimes', 'string', 'max:20', 'unique:students,nis,' . $this->student->id],
            'nisn' => ['sometimes', 'string', 'max:20', 'unique:students,nisn,' . $this->student->id],
            'birth_place' => ['sometimes', 'string', 'max:255'],
            'birth_date' => ['sometimes', 'date'],
            'gender' => ['sometimes', new Enum(Gender::class)],
            'religion' => ['sometimes', 'string', 'max:20'],
            'address' => ['sometimes', 'string'],
            'entry_year' => ['sometimes', 'integer', 'min:1900', 'max:9999'],
            'profile_picture' => ['nullable', 'string', 'max:255'],
            'phone' => ['nullable', 'string', 'max:20'],
        ];
    }
}
```

### 13. `StoreClassScheduleRequest.php`
```php
<?php

namespace App\Http\Requests\ClassSchedule;

use App\Enums\DayOfWeek;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rules\Enum;

class StoreClassScheduleRequest extends FormRequest
{
    public function authorize(): bool
    {
        return true; // Adjust based on authorization logic
    }

    public function rules(): array
    {
        return [
            'teacher_assignment_id' => ['required', 'exists:teacher_assignments,id'],
            'lesson_hour_id' => ['required', 'exists:lesson_hours,id'],
            'day_of_week' => ['required', new Enum(DayOfWeek::class)],
            'academic_year_id' => ['required', 'exists:academic_years,id'],
        ];
    }
}
```

### 14. `UpdateClassScheduleRequest.php`
```php
<?php

namespace App\Http\Requests\ClassSchedule;

use App\Enums\DayOfWeek;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rules\Enum;

class UpdateClassScheduleRequest extends FormRequest
{
    public function authorize(): bool
    {
        return true; // Adjust based on authorization logic
    }

    public function rules(): array
    {
        return [
            'teacher_assignment_id' => ['sometimes', 'exists:teacher_assignments,id'],
            'lesson_hour_id' => ['sometimes', 'exists:lesson_hours,id'],
            'day_of_week' => ['sometimes', new Enum(DayOfWeek::class)],
            'academic_year_id' => ['sometimes', 'exists:academic_years,id'],
        ];
    }
}
```

### 15. `StoreTeacherAttendanceRequest.php` (Updated)
```php
<?php

namespace App\Http\Requests\TeacherAttendance;

use App\Enums\TeacherAttendanceStatus;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rules\Enum;

class StoreTeacherAttendanceRequest extends FormRequest
{
    public function authorize(): bool
    {
        return true; // Adjust based on authorization logic
    }

    public function rules(): array
    {
        return [
            'teacher_id' => ['required', 'exists:teachers,id'],
            'class_schedule_id' => ['required', 'exists:class_schedules,id'],
            'attendance_date' => ['required', 'date'],
            'check_in_time' => ['required', 'date_format:Y-m-d H:i:s'],
            'check_out_time' => ['nullable', 'date_format:Y-m-d H:i:s', 'after:check_in_time'],
            'latitude' => ['required', 'numeric', 'between:-90,90'],
            'longitude' => ['required', 'numeric', 'between:-180,180'],
            'geofence_id' => ['nullable', 'exists:geofences,id'],
            'status' => ['required', new Enum(TeacherAttendanceStatus::class)],
            'is_substitute_teacher' => ['required', 'boolean'],
            'original_teacher_id' => ['nullable', 'exists:teachers,id'],
            'notes' => ['nullable', 'string'],
        ];
    }
}
```

### 16. `UpdateTeacherAttendanceRequest.php` (Updated)
```php
<?php

namespace App\Http\Requests\TeacherAttendance;

use App\Enums\TeacherAttendanceStatus;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rules\Enum;

class UpdateTeacherAttendanceRequest extends FormRequest
{
    public function authorize(): bool
    {
        return true; // Adjust based on authorization logic
    }

    public function rules(): array
    {
        return [
            'teacher_id' => ['sometimes', 'exists:teachers,id'],
            'class_schedule_id' => ['sometimes', 'exists:class_schedules,id'],
            'attendance_date' => ['sometimes', 'date'],
            'check_in_time' => ['sometimes', 'date_format:Y-m-d H:i:s'],
            'check_out_time' => ['nullable', 'date_format:Y-m-d H:i:s', 'after:check_in_time'],
            'latitude' => ['sometimes', 'numeric', 'between:-90,90'],
            'longitude' => ['sometimes', 'numeric', 'between:-180,180'],
            'geofence_id' => ['nullable', 'exists:geofences,id'],
            'status' => ['sometimes', new Enum(TeacherAttendanceStatus::class)],
            'is_substitute_teacher' => ['sometimes', 'boolean'],
            'original_teacher_id' => ['nullable', 'exists:teachers,id'],
            'notes' => ['nullable', 'string'],
        ];
    }
}
```

### 17. `StoreStudentAttendanceRequest.php`
```php
<?php

namespace App\Http\Requests\StudentAttendance;

use App\Enums\StudentAttendanceStatus;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rules\Enum;

class StoreStudentAttendanceRequest extends FormRequest
{
    public function authorize(): bool
    {
        return true; // Adjust based on authorization logic
    }

    public function rules(): array
    {
        return [
            'student_id' => ['required', 'exists:students,id'],
            'teacher_attendance_id' => ['required', 'exists:teacher_attendances,id'],
            'attendance_status' => ['required', new Enum(StudentAttendanceStatus::class)],
            'notes' => ['nullable', 'string'],
        ];
    }
}
```

### 18. `UpdateStudentAttendanceRequest.php`
```php
<?php

namespace App\Http\Requests\StudentAttendance;

use App\Enums\StudentAttendanceStatus;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rules\Enum;

class UpdateStudentAttendanceRequest extends FormRequest
{
    public function authorize(): bool
    {
        return true; // Adjust based on authorization logic
    }

    public function rules(): array
    {
        return [
            'student_id' => ['sometimes', 'exists:students,id'],
            'teacher_attendance_id' => ['sometimes', 'exists:teacher_attendances,id'],
            'attendance_status' => ['sometimes', new Enum(StudentAttendanceStatus::class)],
            'notes' => ['nullable', 'string'],
        ];
    }
}
```

### 19. `StoreTeacherSalaryComponentRequest.php`
```php
<?php

namespace App\Http\Requests\TeacherSalaryComponent;

use App\Enums\CalculationType;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rules\Enum;

class StoreTeacherSalaryComponentRequest extends FormRequest
{
    public function authorize(): bool
    {
        return true; // Adjust based on authorization logic
    }

    public function rules(): array
    {
        return [
            'teacher_id' => ['required', 'exists:teachers,id'],
            'component_name' => ['required', 'string', 'max:255'],
            'amount' => ['required', 'numeric', 'min:0'],
            'calculation_type' => ['required', new Enum(CalculationType::class)],
            'effective_date' => ['required', 'date'],
            'end_date' => ['nullable', 'date', 'after:effective_date'],
        ];
    }
}
```

### 20. `UpdateTeacherSalaryComponentRequest.php`
```php
<?php

namespace App\Http\Requests\TeacherSalaryComponent;

use App\Enums\CalculationType;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rules\Enum;

class UpdateTeacherSalaryComponentRequest extends FormRequest
{
    public function authorize(): bool
    {
        return true; // Adjust based on authorization logic
    }

    public function rules(): array
    {
        return [
            'teacher_id' => ['sometimes', 'exists:teachers,id'],
            'component_name' => ['sometimes', 'string', 'max:255'],
            'amount' => ['sometimes', 'numeric', 'min:0'],
            'calculation_type' => ['sometimes', new Enum(CalculationType::class)],
            'effective_date' => ['sometimes', 'date'],
            'end_date' => ['nullable', 'date', 'after:effective_date'],
        ];
    }
}
```

### 21. `StorePayrollRecordRequest.php`
```php
<?php

namespace App\Http\Requests\PayrollRecord;

use App\Enums\PayrollStatus;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rules\Enum;

class StorePayrollRecordRequest extends FormRequest
{
    public function authorize(): bool
    {
        return true; // Adjust based on authorization logic
    }

    public function rules(): array
    {
        return [
            'teacher_id' => ['required', 'exists:teachers,id'],
            'academic_year_id' => ['required', 'exists:academic_years,id'],
            'period_name' => ['required', 'string', 'max:255'],
            'start_date' => ['required', 'date'],
            'end_date' => ['required', 'date', 'after:start_date'],
            'total_regular_teaching_hours' => ['required', 'numeric', 'min:0'],
            'total_substitute_teaching_hours' => ['required', 'numeric', 'min:0'],
            'status' => ['required', new Enum(PayrollStatus::class)],
            'generated_by_user_id' => ['nullable', 'exists:users,id'],
            'generated_at' => ['nullable', 'date'],
            'paid_at' => ['nullable', 'date'],
        ];
    }
}
```

### 22. `UpdatePayrollRecordRequest.php`
```php
<?php

namespace App\Http\Requests\PayrollRecord;

use App\Enums\PayrollStatus;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rules\Enum;

class UpdatePayrollRecordRequest extends FormRequest
{
    public function authorize(): bool
    {
        return true; // Adjust based on authorization logic
    }

    public function rules(): array
    {
        return [
            'teacher_id' => ['sometimes', 'exists:teachers,id'],
            'academic_year_id' => ['sometimes', 'exists:academic_years,id'],
            'period_name' => ['sometimes', 'string', 'max:255'],
            'start_date' => ['sometimes', 'date'],
            'end_date' => ['sometimes', 'date', 'after:start_date'],
            'total_regular_teaching_hours' => ['sometimes', 'numeric', 'min:0'],
            'total_substitute_teaching_hours' => ['sometimes', 'numeric', 'min:0'],
            'status' => ['sometimes', new Enum(PayrollStatus::class)],
            'generated_by_user_id' => ['nullable', 'exists:users,id'],
            'generated_at' => ['nullable', 'date'],
            'paid_at' => ['nullable', 'date'],
        ];
    }
}
```

### 23. `StoreGeofenceLogRequest.php`
```php
<?php

namespace App\Http\Requests\GeofenceLog;

use App\Enums\GeofenceAction;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rules\Enum;

class StoreGeofenceLogRequest extends FormRequest
{
    public function authorize(): bool
    {
        return true; // Adjust based on authorization logic
    }

    public function rules(): array
    {
        return [
            'teacher_id' => ['required', 'exists:teachers,id'],
            'geofence_id' => ['required', 'exists:geofences,id'],
            'action' => ['required', new Enum(GeofenceAction::class)],
            'latitude' => ['required', 'numeric', 'between:-90,90'],
            'longitude' => ['required', 'numeric', 'between:-180,180'],
            'logged_at' => ['required', 'date'],
        ];
    }
}
```

### 24. `UpdateGeofenceLogRequest.php`
```php
<?php

namespace App\Http\Requests\GeofenceLog;

use App\Enums\GeofenceAction;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rules\Enum;

class UpdateGeofenceLogRequest extends FormRequest
{
    public function authorize(): bool
    {
        return true; // Adjust based on authorization logic
    }

    public function rules(): array
    {
        return [
            'teacher_id' => ['sometimes', 'exists:teachers,id'],
            'geofence_id' => ['sometimes', 'exists:geofences,id'],
            'action' => ['sometimes', new Enum(GeofenceAction::class)],
            'latitude' => ['sometimes', 'numeric', 'between:-90,90'],
            'longitude' => ['sometimes', 'numeric', 'between:-180,180'],
            'logged_at' => ['sometimes', 'date'],
        ];
    }
}
```

---

## Notes
- **Enum Integration**:
  - Enums are defined in the `app/Enums` directory as backed enums with string values to match the database schema.
  - Models use the `casts` property to automatically cast enum fields to their respective enum classes, enabling type-safe operations (e.g., `$model->status === AcademicYearStatus::Active`).
- **Request Validation**:
  - The `Enum` validation rule (`Illuminate\Validation\Rules\Enum`) ensures that only valid enum values are accepted in API requests.
  - Request classes for entities not previously provided (e.g., `AcademicYear`, `Program`, `Shift`, etc.) are included to demonstrate the pattern.
- **Best Practices**:
  - **Type Safety**: Enums provide type safety, reducing errors from invalid values.
  - **Maintainability**: Enums centralize valid values, making it easy to update or extend them.
  - **Validation**: Request classes ensure that only valid enum values are passed to the database.
- **Usage in Code**:
  - In controllers/services, you can use enums directly, e.g.:
    ```php
    if ($academicYear->status === AcademicYearStatus::Active) {
        // Do something
    }
    ```
  - When creating/updating records, pass enum values as strings:
    ```php
    AcademicYear::create([
        'name' => '2024/2025',
        'semester' => Semester::Odd->value,
        'status' => AcademicYearStatus::Planned->value,
        // other fields
    ]);
    ```
- **File Organization**:
  - Place enums in `app/Enums`.
  - Update models in `app/Models`.
  - Place request classes in `app/Http/Requests`.
- **Controllers and Services**:
  - The controllers and services from the previous artifact remain compatible. Ensure that services handle enum values appropriately (e.g., accessing `->value` when saving to the database).
- **Routes**:
  - Add routes for new entities in `routes/api.php`:
    ```php
    Route::apiResource('academic-years', AcademicYearController::class);
    Route::apiResource('programs', ProgramController::class);
    Route::apiResource('shifts', ShiftController::class);
    Route::apiResource('class-schedules', ClassScheduleController::class);
    Route::apiResource('student-attendances', StudentAttendanceController::class);
    Route::apiResource('teacher-salary-components', TeacherSalaryComponentController::class);
    Route::apiResource('payroll-records', PayrollRecordController::class);
    Route::apiResource('geofence-logs', GeofenceLogController::class);
    ```
  - Create corresponding controllers and services for these entities by following the pattern provided in the previous artifact.

This implementation ensures that enums are fully integrated into the `db_rawooh_v2` application, providing type safety, validation, and maintainability for enumerated fields.