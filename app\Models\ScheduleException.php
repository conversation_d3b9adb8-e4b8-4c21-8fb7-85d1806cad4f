<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class ScheduleException extends Model
{
    protected $fillable = [
        'class_schedule_id',
        'exception_date',
        'exception_type',
        'new_start_time',
        'new_end_time',
        'is_cancelled',
        'reason',
        'notes'
    ];

    protected $casts = [
        'exception_date' => 'date',
        'new_start_time' => 'datetime:H:i',
        'new_end_time' => 'datetime:H:i',
        'is_cancelled' => 'boolean',
    ];

    public function classSchedule(): BelongsTo
    {
        return $this->belongsTo(ClassSchedule::class);
    }

    public function scopeCancelled($query)
    {
        return $query->where('is_cancelled', true);
    }

    public function scopeForDate($query, $date)
    {
        return $query->where('exception_date', $date);
    }
}
