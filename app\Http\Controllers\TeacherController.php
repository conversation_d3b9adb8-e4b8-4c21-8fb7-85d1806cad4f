<?php

namespace App\Http\Controllers;

use App\Http\Requests\Teacher\StoreTeacherRequest;
use App\Http\Requests\Teacher\UpdateTeacherRequest;
use App\Models\Teacher;
use Illuminate\Http\RedirectResponse;
use Illuminate\Http\Request;
use Illuminate\View\View;

class TeacherController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index(Request $request): View
    {
        $query = Teacher::with(['user', 'shift', 'teacherAssignments']);

        // Apply search if provided
        if ($request->filled('search')) {
            $search = $request->get('search');
            $query->whereHas('user', function ($q) use ($search) {
                $q->where('name', 'like', "%{$search}%")
                    ->orWhere('username', 'like', "%{$search}%")
                    ->orWhere('email', 'like', "%{$search}%");
            })->orWhere('employee_id', 'like', "%{$search}%")
                ->orWhere('nip', 'like', "%{$search}%")
                ->orWhere('full_name', 'like', "%{$search}%");
        }

        // Apply status filter if provided
        if ($request->filled('status')) {
            $query->where('status', $request->get('status'));
        }

        // Apply employment status filter if provided
        if ($request->filled('employment_status')) {
            $query->where('employment_status', $request->get('employment_status'));
        }

        // Apply shift filter if provided
        if ($request->filled('shift_id')) {
            $query->where('shift_id', $request->get('shift_id'));
        }

        // Apply sorting
        $sortBy = $request->get('sort_by', 'created_at');
        $sortDirection = $request->get('sort_direction', 'desc');
        $query->orderBy($sortBy, $sortDirection);

        $teachers = $query->paginate(15);
        $teachers->appends($request->query());

        return view('teachers.index', compact('teachers'));
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create(): View
    {
        return view('teachers.create');
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(StoreTeacherRequest $request): RedirectResponse
    {
        try {
            $data = $request->validated();

            // Set default status if not provided
            if (!isset($data['status'])) {
                $data['status'] = true;
            }

            $teacher = Teacher::create($data);

            // Log teacher creation
            logger()->info('Teacher created', [
                'teacher_id' => $teacher->id,
                'employee_id' => $teacher->employee_id,
                'created_by' => auth()->id(),
            ]);

            return redirect()
                ->route('teachers.show', $teacher)
                ->with('success', 'Teacher created successfully.');
        } catch (\Exception $e) {
            return back()
                ->withInput()
                ->with('error', 'Failed to create teacher: ' . $e->getMessage());
        }
    }

    /**
     * Display the specified resource.
     */
    public function show(Teacher $teacher): View
    {
        $teacher->load(['user', 'shift', 'teacherAssignments.subject', 'teacherAssignments.classroom']);

        return view('teachers.show', compact('teacher'));
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(Teacher $teacher): View
    {
        return view('teachers.edit', compact('teacher'));
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(UpdateTeacherRequest $request, Teacher $teacher): RedirectResponse
    {
        try {
            $data = $request->validated();

            $teacher->update($data);

            // Log teacher update
            logger()->info('Teacher updated', [
                'teacher_id' => $teacher->id,
                'employee_id' => $teacher->employee_id,
                'updated_by' => auth()->id(),
            ]);

            return redirect()
                ->route('teachers.show', $teacher)
                ->with('success', 'Teacher updated successfully.');
        } catch (\Exception $e) {
            return back()
                ->withInput()
                ->with('error', 'Failed to update teacher: ' . $e->getMessage());
        }
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(Teacher $teacher): RedirectResponse
    {
        try {
            // Check if teacher has active assignments
            if ($teacher->teacherAssignments()->exists()) {
                return back()->with('error', 'Cannot delete teacher with active assignments.');
            }

            // Log teacher deletion before deleting
            logger()->info('Teacher deleted', [
                'teacher_id' => $teacher->id,
                'employee_id' => $teacher->employee_id,
                'deleted_by' => auth()->id(),
            ]);

            $teacher->delete();

            return redirect()
                ->route('teachers.index')
                ->with('success', 'Teacher deleted successfully.');
        } catch (\Exception $e) {
            return back()
                ->with('error', 'Failed to delete teacher: ' . $e->getMessage());
        }
    }
}
