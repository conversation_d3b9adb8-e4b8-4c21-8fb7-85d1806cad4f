<?php

namespace App\Enums;

enum StudentAttendanceStatus: string
{
    case PRESENT = 'present';
    case ABSENT = 'absent';
    case LATE = 'late';
    case SICK = 'sick';
    case PERMISSION = 'permission';
    case ALPHA = 'alpha';

    public function label(): string
    {
        return match($this) {
            self::PRESENT => 'Hadir',
            self::ABSENT => 'Tidak Hadir',
            self::LATE => 'Terlambat',
            self::SICK => 'Sakit',
            self::PERMISSION => 'Izin',
            self::ALPHA => 'Alpha',
        };
    }

    public static function options(): array
    {
        return [
            self::PRESENT->value => self::PRESENT->label(),
            self::ABSENT->value => self::ABSENT->label(),
            self::LATE->value => self::LATE->label(),
            self::SICK->value => self::SICK->label(),
            self::PERMISSION->value => self::PERMISSION->label(),
            self::ALPHA->value => self::ALPHA->label(),
        ];
    }
}
