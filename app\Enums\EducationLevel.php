<?php

namespace App\Enums;

enum EducationLevel: string
{
    case SD = 'SD';
    case SMP = 'SMP';
    case SMA = 'SMA';
    case D3 = 'D3';
    case S1 = 'S1';
    case S2 = 'S2';
    case S3 = 'S3';

    public function label(): string
    {
        return match($this) {
            self::SD => 'Sekolah Dasar',
            self::SMP => 'Sekolah Menengah Pertama',
            self::SMA => 'Sekolah Menengah Atas',
            self::D3 => 'Diploma 3',
            self::S1 => '<PERSON><PERSON>jana (S1)',
            self::S2 => 'Magister (S2)',
            self::S3 => 'Doktor (S3)',
        };
    }

    public static function options(): array
    {
        return [
            self::SD->value => self::SD->label(),
            self::SMP->value => self::SMP->label(),
            self::SMA->value => self::SMA->label(),
            self::D3->value => self::D3->label(),
            self::S1->value => self::S1->label(),
            self::S2->value => self::S2->label(),
            self::S3->value => self::S3->label(),
        ];
    }
}
