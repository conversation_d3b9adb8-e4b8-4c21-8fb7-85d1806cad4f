<?php

namespace App\Http\Controllers;

use App\Http\Requests\Student\StoreStudentRequest;
use App\Http\Requests\Student\UpdateStudentRequest;
use App\Models\Student;
use App\Models\Program;
use Illuminate\Http\RedirectResponse;
use Illuminate\Http\Request;
use Illuminate\View\View;

class StudentController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index(Request $request): View
    {
        $query = Student::with(['program']);

        // Apply search if provided
        if ($request->filled('search')) {
            $search = $request->get('search');
            $query->where(function ($q) use ($search) {
                $q->where('student_id', 'like', "%{$search}%")
                    ->orWhere('nisn', 'like', "%{$search}%")
                    ->orWhere('full_name', 'like', "%{$search}%");
            });
        }

        // Apply status filter if provided
        if ($request->filled('status')) {
            $query->where('status', $request->get('status'));
        }

        // Apply program filter if provided
        if ($request->filled('program_id')) {
            $query->where('program_id', $request->get('program_id'));
        }

        // Apply gender filter if provided
        if ($request->filled('gender')) {
            $query->where('gender', $request->get('gender'));
        }

        // Apply sorting
        $sortBy = $request->get('sort_by', 'enrollment_date');
        $sortDirection = $request->get('sort_direction', 'desc');
        $query->orderBy($sortBy, $sortDirection);

        $students = $query->paginate(15);
        $students->appends($request->query());

        // Get programs for filter dropdown
        $programs = Program::active()->orderBy('name')->get();

        return view('students.index', compact('students', 'programs'));
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create(): View
    {
        $programs = Program::active()->orderBy('name')->get();

        return view('students.create', compact('programs'));
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(StoreStudentRequest $request): RedirectResponse
    {
        try {
            $data = $request->validated();

            // Set default status if not provided
            if (!isset($data['status'])) {
                $data['status'] = 'active';
            }

            $student = Student::create($data);

            // Log student creation
            logger()->info('Student created', [
                'student_id' => $student->id,
                'student_number' => $student->student_id,
                'full_name' => $student->full_name,
                'program_id' => $student->program_id,
                'created_by' => auth()->id(),
            ]);

            return redirect()
                ->route('students.show', $student)
                ->with('success', 'Student created successfully.');
        } catch (\Exception $e) {
            return back()
                ->withInput()
                ->with('error', 'Failed to create student: ' . $e->getMessage());
        }
    }

    /**
     * Display the specified resource.
     */
    public function show(Student $student): View
    {
        $student->load(['program', 'contactInformation', 'parents', 'classrooms', 'attendances']);

        return view('students.show', compact('student'));
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(Student $student): View
    {
        $programs = Program::active()->orderBy('name')->get();

        return view('students.edit', compact('student', 'programs'));
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(UpdateStudentRequest $request, Student $student): RedirectResponse
    {
        try {
            $data = $request->validated();

            $student->update($data);

            // Log student update
            logger()->info('Student updated', [
                'student_id' => $student->id,
                'student_number' => $student->student_id,
                'full_name' => $student->full_name,
                'program_id' => $student->program_id,
                'updated_by' => auth()->id(),
            ]);

            return redirect()
                ->route('students.show', $student)
                ->with('success', 'Student updated successfully.');
        } catch (\Exception $e) {
            return back()
                ->withInput()
                ->with('error', 'Failed to update student: ' . $e->getMessage());
        }
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(Student $student): RedirectResponse
    {
        try {
            // Check if student has active classroom enrollments
            if ($student->classrooms()->wherePivot('is_active', true)->exists()) {
                return back()->with('error', 'Cannot delete student with active classroom enrollments.');
            }

            // Log student deletion before deleting
            logger()->info('Student deleted', [
                'student_id' => $student->id,
                'student_number' => $student->student_id,
                'full_name' => $student->full_name,
                'deleted_by' => auth()->id(),
            ]);

            $student->delete();

            return redirect()
                ->route('students.index')
                ->with('success', 'Student deleted successfully.');
        } catch (\Exception $e) {
            return back()
                ->with('error', 'Failed to delete student: ' . $e->getMessage());
        }
    }
}
