# Database Documentation for `db_rawooh_v2`

This document provides an overview of the database structure for `db_rawooh_v2`, excluding <PERSON><PERSON>'s default tables (`cache`, `cache_locks`, `failed_jobs`, `jobs`, `job_batches`). It includes descriptions of each table, their columns, and relationships. The database is designed to manage a school system, including user authentication, school master data, teacher and student information, schedules, attendance, geofencing, and payroll.

---

## Table Descriptions

### 1. Users
Stores user authentication data for all system users (e.g., admins, teachers, parents).

- **Columns**:
  - `id`: `bigint(20) unsigned`, Auto-incremented primary key.
  - `username`: `varchar(255)`, Unique username.
  - `name`: `varchar(255)`, User's full name.
  - `email`: `varchar(255)`, Unique email address.
  - `photo_path`: `varchar(255)`, Path to user's profile photo (nullable).
  - `password`: `varchar(255)`, Hashed password.
  - `email_verified_at`: `timestamp`, Email verification timestamp (nullable).
  - `phone_number`: `varchar(20)`, User's phone number (nullable).
  - `status`: `tinyint(1)`, User status (1 = Active, 0 = Inactive). Default: 1.
  - `last_login_at`: `timestamp`, Last login timestamp (nullable).
  - `remember_token`: `varchar(100)`, Token for "remember me" functionality (nullable).
  - `created_at`: `timestamp`, Record creation timestamp (nullable).
  - `updated_at`: `timestamp`, Record update timestamp (nullable).
- **Indexes**:
  - Primary Key: `id`
  - Index: `users_email_index` on `email`
- **Notes**: This table uses the `HasRoles` trait from the Spatie Permission package for role-based access control.

### 2. Password Reset Tokens
Stores tokens for password reset requests.

- **Columns**:
  - `email`: `varchar(255)`, Email address (primary key).
  - `token`: `varchar(255)`, Password reset token.
  - `created_at`: `timestamp`, Token creation timestamp (nullable).
- **Indexes**:
  - Primary Key: `email`

### 3. Sessions
Stores user session data for authentication.

- **Columns**:
  - `id`: `varchar(255)`, Session ID (primary key).
  - `user_id`: `bigint(20) unsigned`, References `users.id` (nullable).
  - `ip_address`: `varchar(45)`, User's IP address (nullable).
  - `user_agent`: `text`, User's browser agent (nullable).
  - `payload`: `longtext`, Session data.
  - `last_activity`: `int(11)`, Timestamp of last activity.
- **Indexes**:
  - Primary Key: `id`
  - Index: `sessions_user_id_index` on `user_id`
  - Index: `sessions_last_activity_index` on `last_activity`
- **Foreign Keys**:
  - `user_id` → `users.id` (ON DELETE CASCADE)

### 4. Academic Years
Stores academic year information (e.g., "2024/2025").

- **Columns**:
  - `id`: `bigint(20) unsigned`, Auto-incremented primary key.
  - `name`: `varchar(255)`, Academic year name (e.g., "2024/2025").
  - `semester`: `enum('odd','even')`, Semester type.
  - `start_date`: `date`, Start date of the academic year.
  - `end_date`: `date`, End date of the academic year.
  - `status`: `enum('planned','active','completed')`, Status of the academic year. Default: 'planned'.
  - `created_at`: `timestamp`, Record creation timestamp (nullable).
  - `updated_at`: `timestamp`, Record update timestamp (nullable).
- **Indexes**:
  - Primary Key: `id`
  - Index: `academic_years_status_index` on `status`

### 5. Programs
Stores academic programs (e.g., "Regular Program").

- **Columns**:
  - `id`: `bigint(20) unsigned`, Auto-incremented primary key.
  - `name`: `varchar(255)`, Program name.
  - `code`: `varchar(255)`, Unique program code.
  - `description`: `text`, Program description (nullable).
  - `status`: `enum('active','inactive')`, Program status. Default: 'active'.
  - `created_at`: `timestamp`, Record creation timestamp (nullable).
  - `updated_at`: `timestamp`, Record update timestamp (nullable).
- **Indexes**:
  - Primary Key: `id`
  - Unique Key: `code`
  - Index: `programs_status_index` on `status`

### 6. Shifts
Stores shift information (e.g., "Morning", "Afternoon").

- **Columns**:
  - `id`: `bigint(20) unsigned`, Auto-incremented primary key.
  - `name`: `varchar(255)`, Shift name.
  - `description`: `text`, Shift description (nullable).
  - `status`: `enum('active','inactive')`, Shift status. Default: 'active'.
  - `created_at`: `timestamp`, Record creation timestamp (nullable).
  - `updated_at`: `timestamp`, Record update timestamp (nullable).
- **Indexes**:
  - Primary Key: `id`
  - Index: `shifts_status_index` on `status`

### 7. Classrooms
Stores classroom information (e.g., "VII A").

- **Columns**:
  - `id`: `bigint(20) unsigned`, Auto-incremented primary key.
  - `name`: `varchar(255)`, Classroom name (e.g., "VII A").
  - `level`: `enum('7','8','9')`, Grade level (SMP levels).
  - `capacity`: `int(11)`, Classroom capacity.
  - `program_id`: `bigint(20) unsigned`, References `programs.id`.
  - `shift_id`: `bigint(20) unsigned`, References `shifts.id` (nullable).
  - `teacher_id`: `bigint(20) unsigned`, References `teachers.id` (homeroom teacher, nullable).
  - `academic_year_id`: `bigint(20) unsigned`, References `academic_years.id`.
  - `status`: `enum('active','inactive')`, Classroom status. Default: 'active'.
  - `description`: `text`, Classroom description (nullable).
  - `created_at`: `timestamp`, Record creation timestamp (nullable).
  - `updated_at`: `timestamp`, Record update timestamp (nullable).
- **Indexes**:
  - Primary Key: `id`
  - Index: `classrooms_program_id_foreign` on `program_id`
  - Index: `classrooms_teacher_id_foreign` on `teacher_id`
  - Index: `classrooms_academic_year_id_foreign` on `academic_year_id`
  - Index: `classrooms_shift_id_foreign` on `shift_id`
  - Index: `classrooms_status_index` on `status`
- **Foreign Keys**:
  - `program_id` → `programs.id`
  - `shift_id` → `shifts.id` (ON DELETE SET NULL)
  - `teacher_id` → `teachers.id` (ON DELETE SET NULL)
  - `academic_year_id` → `academic_years.id`

### 8. Subjects
Stores subject information (e.g., "Mathematics").

- **Columns**:
  - `id`: `bigint(20) unsigned`, Auto-incremented primary key.
  - `name`: `varchar(255)`, Subject name.
  - `program_id`: `bigint(20) unsigned`, References `programs.id`.
  - `created_at`: `timestamp`, Record creation timestamp (nullable).
  - `updated_at`: `timestamp`, Record update timestamp (nullable).
- **Indexes**:
  - Primary Key: `id`
  - Index: `subjects_program_id_foreign` on `program_id`
- **Foreign Keys**:
  - `program_id` → `programs.id`

### 9. Lesson Hours
Stores lesson hour schedules (e.g., "First Hour").

- **Columns**:
  - `id`: `bigint(20) unsigned`, Auto-incremented primary key.
  - `name`: `varchar(255)`, Lesson hour name.
  - `start_time`: `time`, Start time of the lesson.
  - `end_time`: `time`, End time of the lesson.
  - `sequence`: `int(11)`, Order of the lesson hour.
  - `created_at`: `timestamp`, Record creation timestamp (nullable).
  - `updated_at`: `timestamp`, Record update timestamp (nullable).
- **Indexes**:
  - Primary Key: `id`
  - Unique Key: `lesson_hours_unique_time` on (`start_time`, `end_time`)

### 10. Schedule Exceptions
Stores exceptions to class schedules (e.g., holidays).

- **Columns**:
  - `id`: `bigint(20) unsigned`, Auto-incremented primary key.
  - `class_schedule_id`: `bigint(20) unsigned`, References `class_schedules.id` (nullable for global holidays).
  - `exception_date`: `date`, Date of the exception.
  - `reason`: `varchar(255)`, Reason for the exception (e.g., "Hari Raya Idul Fitri").
  - `created_at`: `timestamp`, Record creation timestamp (nullable).
  - `updated_at`: `timestamp`, Record update timestamp (nullable).
- **Indexes**:
  - Primary Key: `id`
  - Unique Key: `schedule_exceptions_unique` on (`class_schedule_id`, `exception_date`)
  - Index: `schedule_exceptions_date_index` on `exception_date`
- **Foreign Keys**:
  - `class_schedule_id` → `class_schedules.id` (ON DELETE CASCADE)

### 11. Geofences
Stores geofence data for location-based attendance tracking.

- **Columns**:
  - `id`: `bigint(20) unsigned`, Auto-incremented primary key.
  - `name`: `varchar(255)`, Geofence name (e.g., "Sekolah SMP Rawooh").
  - `latitude`: `decimal(10,8)`, Geofence center latitude.
  - `longitude`: `decimal(10,8)`, Geofence center longitude.
  - `radius`: `decimal(8,2)`, Geofence radius in meters.
  - `created_at`: `timestamp`, Record creation timestamp (nullable).
  - `updated_at`: `timestamp`, Record update timestamp (nullable).
- **Indexes**:
  - Primary Key: `id`

### 12. Teachers
Stores teacher-specific information.

- **Columns**:
  - `id`: `bigint(20) unsigned`, Auto-incremented primary key.
  - `user_id`: `bigint(20) unsigned`, References `users.id` (unique).
  - `nip`: `varchar(255)`, Teacher identification number (nullable, unique).
  - `birth_place`: `varchar(255)`, Teacher's birth place.
  - `birth_date`: `date`, Teacher's birth date.
  - `gender`: `enum('male','female')`, Teacher's gender.
  - `phone_number`: `varchar(20)`, Teacher's phone number.
  - `full_address`: `text`, Teacher's address.
  - `gps_consent`: `tinyint(1)`, Consent for GPS tracking (0 = No, 1 = Yes). Default: 0.
  - `gps_consent_given_at`: `timestamp`, Timestamp of GPS consent (nullable).
  - `created_at`: `timestamp`, Record creation timestamp (nullable).
  - `updated_at`: `timestamp`, Record update timestamp (nullable).
- **Indexes**:
  - Primary Key: `id`
  - Unique Key: `nip`
  - Index: `teachers_user_id_foreign` on `user_id`
- **Foreign Keys**:
  - `user_id` → `users.id` (ON DELETE CASCADE)

### 13. Parents
Stores parent information.

- **Columns**:
  - `id`: `bigint(20) unsigned`, Auto-incremented primary key.
  - `user_id`: `bigint(20) unsigned`, References `users.id` (nullable for parents without login).
  - `name`: `varchar(255)`, Parent's name.
  - `phone_number`: `varchar(15)`, Parent's phone number.
  - `occupation`: `varchar(100)`, Parent's occupation (nullable).
  - `address`: `text`, Parent's address (nullable).
  - `created_at`: `timestamp`, Record creation timestamp (nullable).
  - `updated_at`: `timestamp`, Record update timestamp (nullable).
- **Indexes**:
  - Primary Key: `id`
  - Index: `parents_user_id_foreign` on `user_id`
- **Foreign Keys**:
  - `user_id` → `users.id` (ON DELETE SET NULL)

### 14. Students
Stores student information.

- **Columns**:
  - `id`: `bigint(20) unsigned`, Auto-incremented primary key.
  - `user_id`: `bigint(20) unsigned`, References `users.id` (unique).
  - `nis`: `varchar(20)`, Student identification number (unique).
  - `nisn`: `varchar(20)`, National student identification number (unique).
  - `birth_place`: `varchar(255)`, Student's birth place.
  - `birth_date`: `date`, Student's birth date.
  - `gender`: `enum('male','female')`, Student's gender.
  - `religion`: `varchar(20)`, Student's religion.
  - `address`: `text`, Student's address.
  - `entry_year`: `year(4)`, Student's entry year.
  - `profile_picture`: `varchar(255)`, Path to student's profile picture (nullable).
  - `phone`: `varchar(20)`, Student's phone number (nullable).
  - `created_at`: `timestamp`, Record creation timestamp (nullable).
  - `updated_at`: `timestamp`, Record update timestamp (nullable).
- **Indexes**:
  - Primary Key: `id`
  - Unique Key: `nis`
  - Unique Key: `nisn`
  - Index: `students_user_id_foreign` on `user_id`
  - Index: `students_nis_index` on `nis`
  - Index: `students_nisn_index` on `nisn`
- **Foreign Keys**:
  - `user_id` → `users.id` (ON DELETE CASCADE)

### 15. Student Parents
Maps relationships between students and their parents.

- **Columns**:
  - `id`: `bigint(20) unsigned`, Auto-incremented primary key.
  - `student_id`: `bigint(20) unsigned`, References `students.id`.
  - `parent_id`: `bigint(20) unsigned`, References `parents.id`.
  - `relation_type`: `varchar(50)`, Relationship type (e.g., "Father", "Mother", "Guardian") (nullable).
  - `created_at`: `timestamp`, Record creation timestamp (nullable).
  - `updated_at`: `timestamp`, Record update timestamp (nullable).
- **Indexes**:
  - Primary Key: `id`
  - Unique Key: `student_parent_unique` on (`student_id`, `parent_id`)
  - Index: `student_parents_student_id_foreign` on `student_id`
  - Index: `student_parents_parent_id_foreign` on `parent_id`
- **Foreign Keys**:
  - `student_id` → `students.id` (ON DELETE CASCADE)
  - `parent_id` → `parents.id` (ON DELETE CASCADE)

### 16. Classroom Students
Maps students to classrooms for a specific academic year.

- **Columns**:
  - `id`: `bigint(20) unsigned`, Auto-incremented primary key.
  - `classroom_id`: `bigint(20) unsigned`, References `classrooms.id`.
  - `student_id`: `bigint(20) unsigned`, References `students.id`.
  - `academic_year_id`: `bigint(20) unsigned`, References `academic_years.id`.
  - `created_at`: `timestamp`, Record creation timestamp (nullable).
  - `updated_at`: `timestamp`, Record update timestamp (nullable).
- **Indexes**:
  - Primary Key: `id`
  - Unique Key: `classroom_students_unique` on (`classroom_id`, `student_id`, `academic_year_id`)
  - Index: `classroom_students_classroom_id_foreign` on `classroom_id`
  - Index: `classroom_students_student_id_foreign` on `student_id`
  - Index: `classroom_students_academic_year_id_foreign` on `academic_year_id`
- **Foreign Keys**:
  - `classroom_id` → `classrooms.id` (ON DELETE CASCADE)
  - `student_id` → `students.id` (ON DELETE CASCADE)
  - `academic_year_id` → `academic_years.id`

### 17. Teacher Assignments
Stores teacher assignments to subjects and classrooms for an academic year.

- **Columns**:
  - `id`: `bigint(20) unsigned`, Auto-incremented primary key.
  - `teacher_id`: `bigint(20) unsigned`, References `teachers.id`.
  - `subject_id`: `bigint(20) unsigned`, References `subjects.id`.
  - `classroom_id`: `bigint(20) unsigned`, References `classrooms.id`.
  - `academic_year_id`: `bigint(20) unsigned`, References `academic_years.id`.
  - `is_homeroom_teacher`: `tinyint(1)`, Indicates if the teacher is a homeroom teacher (0 = No, 1 = Yes). Default: 0.
  - `created_at`: `timestamp`, Record creation timestamp (nullable).
  - `updated_at`: `timestamp`, Record update timestamp (nullable).
- **Indexes**:
  - Primary Key: `id`
  - Unique Key: `teacher_assignments_unique` on (`teacher_id`, `subject_id`, `classroom_id`, `academic_year_id`)
  - Index: `teacher_assignments_teacher_id_foreign` on `teacher_id`
  - Index: `teacher_assignments_subject_id_foreign` on `subject_id`
  - Index: `teacher_assignments_classroom_id_foreign` on `classroom_id`
  - Index: `teacher_assignments_academic_year_id_foreign` on `academic_year_id`
- **Foreign Keys**:
  - `teacher_id` → `teachers.id` (ON DELETE CASCADE)
  - `subject_id` → `subjects.id`
  - `classroom_id` → `classrooms.id` (ON DELETE CASCADE)
  - `academic_year_id` → `academic_years.id`

### 18. Class Schedules
Stores class schedules for teacher assignments.

- **Columns**:
  - `id`: `bigint(20) unsigned`, Auto-incremented primary key.
  - `teacher_assignment_id`: `bigint(20) unsigned`, References `teacher_assignments.id`.
  - `lesson_hour_id`: `bigint(20) unsigned`, References `lesson_hours.id`.
  - `day_of_week`: `enum('monday','tuesday','wednesday','thursday','friday','saturday','sunday')`, Day of the week.
  - `academic_year_id`: `bigint(20) unsigned`, References `academic_years.id`.
  - `created_at`: `timestamp`, Record creation timestamp (nullable).
  - `updated_at`: `timestamp`, Record update timestamp (nullable).
- **Indexes**:
  - Primary Key: `id`
  - Unique Key: `class_schedules_unique` on (`teacher_assignment_id`, `lesson_hour_id`, `day_of_week`, `academic_year_id`)
  - Index: `class_schedules_lesson_hour_id_foreign` on `lesson_hour_id`
  - Index: `class_schedules_academic_year_id_foreign` on `academic_year_id`
  - Index: `class_schedules_day_of_week_index` on `day_of_week`
- **Foreign Keys**:
  - `teacher_assignment_id` → `teacher_assignments.id` (ON DELETE CASCADE)
  - `lesson_hour_id` → `lesson_hours.id`
  - `academic_year_id` → `academic_years.id`

### 19. Teacher Attendances
Stores teacher attendance records with geofencing data.

- **Columns**:
  - `id`: `bigint(20) unsigned`, Auto-incremented primary key.
  - `teacher_id`: `bigint(20) unsigned`, References `teachers.id`.
  - `class_schedule_id`: `bigint(20) unsigned`, References `class_schedules.id`.
  - `attendance_date`: `date`, Date of attendance.
  - `check_in_time`: `datetime`, Check-in time.
  - `check_out_time`: `datetime`, Check-out time (nullable).
  - `latitude`: `decimal(10,8)`, Check-in latitude.
  - `longitude`: `decimal(10,8)`, Check-in longitude.
  - `geofence_id`: `bigint(20) unsigned`, References `geofences.id` (nullable).
  - `status`: `enum('present','absent','late','substitute')`, Attendance status.
  - `is_substitute_teacher`: `tinyint(1)`, Indicates if a substitute teacher (0 = No, 1 = Yes). Default: 0.
  - `original_teacher_id`: `bigint(20) unsigned`, References `teachers.id` (nullable, for substitutes).
  - `notes`: `text`, Additional notes (nullable).
  - `created_at`: `timestamp`, Record creation timestamp (nullable).
  - `updated_at`: `timestamp`, Record update timestamp (nullable).
- **Indexes**:
  - Primary Key: `id`
  - Unique Key: `teacher_attendance_unique` on (`teacher_id`, `class_schedule_id`, `attendance_date`)
  - Index: `teacher_attendances_teacher_id_foreign` on `teacher_id`
  - Index: `teacher_attendances_class_schedule_id_foreign` on `class_schedule_id`
  - Index: `teacher_attendances_original_teacher_id_foreign` on `original_teacher_id`
  - Index: `teacher_attendances_geofence_id_foreign` on `geofence_id`
  - Index: `teacher_attendances_attendance_date_index` on `attendance_date`
  - Index: `teacher_attendances_status_index` on `status`
- **Foreign Keys**:
  - `teacher_id` → `teachers.id` (ON DELETE CASCADE)
  - `class_schedule_id` → `class_schedules.id` (ON DELETE CASCADE)
  - `original_teacher_id` → `teachers.id` (ON DELETE SET NULL)
  - `geofence_id` → `geofences.id` (ON DELETE SET NULL)

### 20. Student Attendances
Stores student attendance records linked to teacher attendance.

- **Columns**:
  - `id`: `bigint(20) unsigned`, Auto-incremented primary key.
  - `student_id`: `bigint(20) unsigned`, References `students.id`.
  - `teacher_attendance_id`: `bigint(20) unsigned`, References `teacher_attendances.id`.
  - `attendance_status`: `enum('present','absent','sick','permission','late')`, Attendance status.
  - `notes`: `text`, Additional notes (nullable).
  - `created_at`: `timestamp`, Record creation timestamp (nullable).
  - `updated_at`: `timestamp`, Record update timestamp (nullable).
- **Indexes**:
  - Primary Key: `id`
  - Unique Key: `student_attendance_unique` on (`student_id`, `teacher_attendance_id`)
  - Index: `student_attendances_student_id_foreign` on `student_id`
  - Index: `student_attendances_teacher_attendance_id_foreign` on `teacher_attendance_id`
  - Index: `student_attendances_attendance_status_index` on `attendance_status`
- **Foreign Keys**:
  - `student_id` → `students.id` (ON DELETE CASCADE)
  - `teacher_attendance_id` → `teacher_attendances.id` (ON DELETE CASCADE)

### 21. Geofence Logs
Stores logs of teacher geofence entries and exits.

- **Columns**:
  - `id`: `bigint(20) unsigned`, Auto-incremented primary key.
  - `teacher_id`: `bigint(20) unsigned`, References `teachers.id`.
  - `geofence_id`: `bigint(20) unsigned`, References `geofences.id`.
  - `action`: `enum('enter','exit')`, Geofence action.
  - `latitude`: `decimal(10,8)`, Logged latitude.
  - `longitude`: `decimal(10,8)`, Logged longitude.
  - `logged_at`: `timestamp`, Timestamp of the log (default: CURRENT_TIMESTAMP).
  - `created_at`: `timestamp`, Record creation timestamp (nullable).
  - `updated_at`: `timestamp`, Record update timestamp (nullable).
- **Indexes**:
  - Primary Key: `id`
  - Index: `geofence_logs_teacher_id_foreign` on `teacher_id`
  - Index: `geofence_logs_geofence_id_foreign` on `geofence_id`
  - Index: `geofence_logs_logged_at_index` on `logged_at`
- **Foreign Keys**:
  - `teacher_id` → `teachers.id` (ON DELETE CASCADE)
  - `geofence_id` → `geofences.id` (ON DELETE CASCADE)

### 22. Teacher Salary Components
Stores salary components for teachers (e.g., base rate, allowances).

- **Columns**:
  - `id`: `bigint(20) unsigned`, Auto-incremented primary key.
  - `teacher_id`: `bigint(20) unsigned`, References `teachers.id`.
  - `component_name`: `varchar(255)`, Salary component name (e.g., "Base Rate per Hour").
  - `amount`: `decimal(15,2)`, Component amount.
  - `calculation_type`: `enum('per_hour','fixed','percentage')`, Calculation type.
  - `effective_date`: `date`, Start date of the component.
  - `end_date`: `date`, End date of the component (nullable).
  - `created_at`: `timestamp`, Record creation timestamp (nullable).
  - `updated_at`: `timestamp`, Record update timestamp (nullable).
- **Indexes**:
  - Primary Key: `id`
  - Index: `teacher_salary_components_teacher_id_foreign` on `teacher_id`
  - Index: `teacher_salary_components_effective_date_index` on `effective_date`
- **Foreign Keys**:
  - `teacher_id` → `teachers.id` (ON DELETE CASCADE)

### 23. Payroll Records
Stores payroll records for teachers.

- **Columns**:
  - `id`: `bigint(20) unsigned`, Auto-incremented primary key.
  - `teacher_id`: `bigint(20) unsigned`, References `teachers.id`.
  - `academic_year_id`: `bigint(20) unsigned`, References `academic_years.id`.
  - `period_name`: `varchar(255)`, Payroll period (e.g., "Juli 2025").
  - `start_date`: `date`, Start date of the payroll period.
  - `end_date`: `date`, End date of the payroll period.
  - `total_regular_teaching_hours`: `decimal(8,2)`, Total regular teaching hours. Default: 0.00.
  - `total_substitute_teaching_hours`: `decimal(8,2)`, Total substitute teaching hours. Default: 0.00.
  - `status`: `enum('generated','paid','pending')`, Payroll status. Default: 'generated'.
  - `generated_by_user_id`: `bigint(20) unsigned`, References `users.id` (nullable).
  - `generated_at`: `timestamp`, Payroll generation timestamp (nullable).
  - `paid_at`: `timestamp`, Payment timestamp (nullable).
  - `created_at`: `timestamp`, Record creation timestamp (nullable).
  - `updated_at`: `timestamp`, Record update timestamp (nullable).
- **Indexes**:
  - Primary Key: `id`
  - Unique Key: `payroll_records_unique_period` on (`teacher_id`, `period_name`, `academic_year_id`)
  - Index: `payroll_records_teacher_id_foreign` on `teacher_id`
  - Index: `payroll_records_academic_year_id_foreign` on `academic_year_id`
  - Index: `payroll_records_generated_by_user_id_foreign` on `generated_by_user_id`
  - Index: `payroll_records_status_index` on `status`
  - Index: `payroll_records_start_date_end_date_index` on (`start_date`, `end_date`)
- **Foreign Keys**:
  - `teacher_id` → `teachers.id` (ON DELETE CASCADE)
  - `academic_year_id` → `academic_years.id`
  - `generated_by_user_id` → `users.id` (ON DELETE SET NULL)

---

## Relationships

The database uses foreign key constraints to enforce relationships. Below is a summary of the key relationships:

1. **Users ↔ Teachers, Parents, Students**:
   - `teachers.user_id` → `users.id` (1:1, ON DELETE CASCADE)
   - `parents.user_id` → `users.id` (1:1, nullable, ON DELETE SET NULL)
   - `students.user_id` → `users.id` (1:1, ON DELETE CASCADE)
   - **Description**: Each teacher and student must have a corresponding user account. Parents may have a user account for system access.

2. **Sessions ↔ Users**:
   - `sessions.user_id` → `users.id` (1:N, nullable, ON DELETE CASCADE)
   - **Description**: Sessions are tied to users for authentication.

3. **Classrooms ↔ Programs, Shifts, Teachers, Academic Years**:
   - `classrooms.program_id` → `programs.id` (N:1)
   - `classrooms.shift_id` → `shifts.id` (N:1, nullable, ON DELETE SET NULL)
   - `classrooms.teacher_id` → `teachers.id` (N:1, nullable, ON DELETE SET NULL)
   - `classrooms.academic_year_id` → `academic_years.id` (N:1)
   - **Description**: Classrooms are associated with a program, an optional shift, an optional homeroom teacher, and an academic year.

4. **Subjects ↔ Programs**:
   - `subjects.program_id` → `programs.id` (N:1)
   - **Description**: Subjects are linked to specific programs.

5. **Classroom Students ↔ Classrooms, Students, Academic Years**:
   - `classroom_students.classroom_id` → `classrooms.id` (N:1, ON DELETE CASCADE)
   - `classroom_students.student_id` → `students.id` (N:1, ON DELETE CASCADE)
   - `classroom_students.academic_year_id` → `academic_years.id` (N:1)
   - **Description**: Maps students to classrooms for a specific academic year.

6. **Student Parents ↔ Students, Parents**:
   - `student_parents.student_id` → `students.id` (N:1, ON DELETE CASCADE)
   - `student_parents.parent_id` → `parents.id` (N:1, ON DELETE CASCADE)
   - **Description**: Defines relationships between students and their parents/guardians.

7. **Teacher Assignments ↔ Teachers, Subjects, Classrooms, Academic Years**:
   - `teacher_assignments.teacher_id` → `teachers.id` (N:1, ON DELETE CASCADE)
   - `teacher_assignments.subject_id` → `subjects.id` (N:1)
   - `teacher_assignments.classroom_id` → `classrooms.id` (N:1, ON DELETE CASCADE)
   - `teacher_assignments.academic_year_id` → `academic_years.id` (N:1)
   - **Description**: Assigns teachers to teach specific subjects in classrooms for an academic year.

8. **Class Schedules ↔ Teacher Assignments, Lesson Hours, Academic Years**:
   - `class_schedules.teacher_assignment_id` → `teacher_assignments.id` (N:1, ON DELETE CASCADE)
   - `class_schedules.lesson_hour_id` → `lesson_hours.id` (N:1)
   - `class_schedules.academic_year_id` → `academic_years.id` (N:1)
   - **Description**: Defines the schedule for teacher assignments, including lesson hours and days of the week.

9. **Schedule Exceptions ↔ Class Schedules**:
   - `schedule_exceptions.class_schedule_id` → `class_schedules.id` (N:1, nullable, ON DELETE CASCADE)
   - **Description**: Records exceptions (e.g., holidays) for class schedules or globally.

10. **Teacher Attendances ↔ Teachers, Class Schedules, Geofences, Teachers (Original)**:
    - `teacher_attendances.teacher_id` → `teachers.id` (N:1, ON DELETE CASCADE)
    - `teacher_attendances.class_schedule_id` → `class_schedules.id` (N:1, ON DELETE CASCADE)
    - `teacher_attendances.geofence_id` → `geofences.id` (N:1, nullable, ON DELETE SET NULL)
    - `teacher_attendances.original_teacher_id` → `teachers.id` (N:1, nullable, ON DELETE SET NULL)
    - **Description**: Tracks teacher attendance, including geofence data and substitute teacher information.

11. **Student Attendances ↔ Students, Teacher Attendances**:
    - `student_attendances.student_id` → `students.id` (N:1, ON DELETE CASCADE)
    - `student_attendances.teacher_attendance_id` → `teacher_attendances.id` (N:1, ON DELETE CASCADE)
    - **Description**: Records student attendance for specific teacher attendance instances.

12. **Geofence Logs ↔ Teachers, Geofences**:
    - `geofence_logs.teacher_id` → `teachers.id` (N:1, ON DELETE CASCADE)
    - `geofence_logs.geofence_id` → `geofences.id` (N:1, ON DELETE CASCADE)
    - **Description**: Logs teacher entries and exits from geofenced areas.

13. **Teacher Salary Components ↔ Teachers**:
    - `teacher_salary_components.teacher_id` → `teachers.id` (N:1, ON DELETE CASCADE)
    - **Description**: Defines salary components for teachers (e.g., hourly rate, allowances).

14. **Payroll Records ↔ Teachers, Academic Years, Users**:
    - `payroll_records.teacher_id` → `teachers.id` (N:1, ON DELETE CASCADE)
    - `payroll_records.academic_year_id` → `academic_years.id` (N:1)
    - `payroll_records.generated_by_user_id` → `users.id` (N:1, nullable, ON DELETE SET NULL)
    - **Description**: Stores payroll records for teachers, including teaching hours and status.

---

## Notes
- **Character Set and Collation**: All tables use `utf8mb4` with `utf8mb4_unicode_ci` collation for Unicode support.
- **Foreign Key Constraints**: Most relationships use `ON DELETE CASCADE` to maintain data integrity, with some nullable foreign keys using `ON DELETE SET NULL`.
- **Spatie Permission Tables**: The database assumes the use of Spatie's permission package for role-based access control (`permissions`, `roles`, `model_has_permissions`, `model_has_roles`, `role_has_permissions`), but these are not detailed here as they are auto-generated.
- **Indexes**: Indexes are used to optimize queries on frequently searched columns (e.g., `status`, `email`, `attendance_date`).
- **Unique Constraints**: Unique keys ensure data integrity (e.g., no duplicate classroom-student assignments or teacher attendances for the same date and schedule).

This structure supports a comprehensive school management system with features for user management, scheduling, attendance tracking with geofencing, and payroll processing.