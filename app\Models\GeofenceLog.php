<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\MorphTo;

class GeofenceLog extends Model
{
    protected $fillable = [
        'geofence_id',
        'loggable_type',
        'loggable_id',
        'event_type',
        'latitude',
        'longitude',
        'event_timestamp',
        'notes'
    ];

    protected $casts = [
        'latitude' => 'decimal:8',
        'longitude' => 'decimal:8',
        'event_timestamp' => 'datetime',
    ];

    public function geofence(): BelongsTo
    {
        return $this->belongsTo(Geofence::class);
    }

    public function loggable(): MorphTo
    {
        return $this->morphTo();
    }

    public function scopeEntry($query)
    {
        return $query->where('event_type', 'entry');
    }

    public function scopeExit($query)
    {
        return $query->where('event_type', 'exit');
    }

    public function scopeForDate($query, $date)
    {
        return $query->whereDate('event_timestamp', $date);
    }
}
