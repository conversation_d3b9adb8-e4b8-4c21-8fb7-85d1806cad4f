<?php

namespace App\Enums;

enum RelationType: string
{
    case FATHER = 'father';
    case MOTHER = 'mother';
    case GUAR<PERSON>AN = 'guardian';
    case STEPFATHER = 'stepfather';
    case STEPMOTHER = 'stepmother';
    case G<PERSON>NDFATHER = 'grandfather';
    case G<PERSON><PERSON>MOTHER = 'grandmother';
    case OTHER = 'other';

    public function label(): string
    {
        return match($this) {
            self::FATHER => 'Ayah',
            self::MOTHER => 'Ibu',
            self::GUARDIAN => 'Wali',
            self::STEPFATHER => 'Ayah Tiri',
            self::STEPMOTHER => 'Ibu Tiri',
            self::GRANDFATHER => 'Kakek',
            self::GRANDMOTHER => 'Nenek',
            self::OTHER => 'Lainnya',
        };
    }

    public static function options(): array
    {
        return [
            self::FATHER->value => self::FATHER->label(),
            self::MOTHER->value => self::MOTHER->label(),
            self::GUARDIAN->value => self::GUARDIAN->label(),
            self::STEPFATHER->value => self::STEPFATHER->label(),
            self::STEPMOTHER->value => self::STEPMOTHER->label(),
            self::GRANDFATHER->value => self::GRANDFATHER->label(),
            self::GRANDMOTHER->value => self::GRANDMOTHER->label(),
            self::OTHER->value => self::OTHER->label(),
        ];
    }
}
