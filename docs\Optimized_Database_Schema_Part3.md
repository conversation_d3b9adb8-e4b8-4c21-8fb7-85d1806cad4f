# Optimized Database Schema Part 3 - Attendance & Payroll Tables

## Attendance Management Tables

### 19. Teacher Attendances Table (Enhanced)
```sql
CREATE TABLE teacher_attendances (
    id BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    teacher_id BIGINT UNSIGNED NOT NULL,
    class_schedule_id BIGINT UNSIGNED NULL,
    academic_year_id BIGINT UNSIGNED NOT NULL,
    attendance_date DATE NOT NULL,
    scheduled_start_time TIME NOT NULL,
    scheduled_end_time TIME NOT NULL,
    actual_start_time TIME NULL,
    actual_end_time TIME NULL,
    check_in_location POINT NULL, -- Spatial data type for GPS
    check_out_location POINT NULL,
    check_in_method ENUM('manual', 'geofence', 'qr_code', 'biometric') DEFAULT 'manual',
    check_out_method ENUM('manual', 'geofence', 'qr_code', 'biometric') DEFAULT 'manual',
    status ENUM('present', 'absent', 'late', 'early_leave', 'sick', 'permission', 'official_duty') NOT NULL,
    late_minutes SMALLINT UNSIGNED DEFAULT 0,
    early_leave_minutes SMALLINT UNSIGNED DEFAULT 0,
    overtime_minutes SMALLINT UNSIGNED DEFAULT 0,
    notes TEXT NULL,
    approved_by BIGINT UNSIGNED NULL,
    approved_at TIMESTAMP NULL,
    created_at TIMESTAMP NULL,
    updated_at TIMESTAMP NULL,
    
    INDEX idx_teacher (teacher_id),
    INDEX idx_class_schedule (class_schedule_id),
    INDEX idx_academic_year (academic_year_id),
    INDEX idx_attendance_date (attendance_date),
    INDEX idx_status (status),
    INDEX idx_teacher_date (teacher_id, attendance_date),
    INDEX idx_date_status (attendance_date, status),
    INDEX idx_approved_by (approved_by),
    SPATIAL INDEX idx_check_in_location (check_in_location),
    SPATIAL INDEX idx_check_out_location (check_out_location),
    UNIQUE KEY unique_teacher_schedule_date (teacher_id, class_schedule_id, attendance_date),
    FOREIGN KEY (teacher_id) REFERENCES teachers(id) ON DELETE CASCADE,
    FOREIGN KEY (class_schedule_id) REFERENCES class_schedules(id) ON DELETE SET NULL,
    FOREIGN KEY (academic_year_id) REFERENCES academic_years(id) ON DELETE CASCADE,
    FOREIGN KEY (approved_by) REFERENCES users(id) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
PARTITION BY RANGE (YEAR(attendance_date)) (
    PARTITION p2024 VALUES LESS THAN (2025),
    PARTITION p2025 VALUES LESS THAN (2026),
    PARTITION p2026 VALUES LESS THAN (2027),
    PARTITION p_future VALUES LESS THAN MAXVALUE
);
```

### 20. Student Attendances Table (Enhanced)
```sql
CREATE TABLE student_attendances (
    id BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    student_id BIGINT UNSIGNED NOT NULL,
    teacher_attendance_id BIGINT UNSIGNED NOT NULL,
    classroom_id BIGINT UNSIGNED NOT NULL,
    subject_id BIGINT UNSIGNED NOT NULL,
    attendance_date DATE NOT NULL,
    lesson_hour_id BIGINT UNSIGNED NOT NULL,
    attendance_status ENUM('present', 'absent', 'late', 'sick', 'permission', 'alpha') NOT NULL,
    late_minutes SMALLINT UNSIGNED DEFAULT 0,
    notes TEXT NULL,
    marked_by BIGINT UNSIGNED NULL, -- Teacher who marked attendance
    marked_at TIMESTAMP NULL,
    parent_notified BOOLEAN DEFAULT FALSE,
    parent_notified_at TIMESTAMP NULL,
    follow_up_required BOOLEAN DEFAULT FALSE,
    follow_up_notes TEXT NULL,
    created_at TIMESTAMP NULL,
    updated_at TIMESTAMP NULL,
    
    INDEX idx_student (student_id),
    INDEX idx_teacher_attendance (teacher_attendance_id),
    INDEX idx_classroom (classroom_id),
    INDEX idx_subject (subject_id),
    INDEX idx_lesson_hour (lesson_hour_id),
    INDEX idx_attendance_date (attendance_date),
    INDEX idx_attendance_status (attendance_status),
    INDEX idx_student_date (student_id, attendance_date),
    INDEX idx_marked_by (marked_by),
    INDEX idx_parent_notified (parent_notified),
    INDEX idx_follow_up (follow_up_required),
    UNIQUE KEY unique_student_lesson_attendance (student_id, teacher_attendance_id, lesson_hour_id),
    FOREIGN KEY (student_id) REFERENCES students(id) ON DELETE CASCADE,
    FOREIGN KEY (teacher_attendance_id) REFERENCES teacher_attendances(id) ON DELETE CASCADE,
    FOREIGN KEY (classroom_id) REFERENCES classrooms(id) ON DELETE CASCADE,
    FOREIGN KEY (subject_id) REFERENCES subjects(id) ON DELETE CASCADE,
    FOREIGN KEY (lesson_hour_id) REFERENCES lesson_hours(id) ON DELETE CASCADE,
    FOREIGN KEY (marked_by) REFERENCES users(id) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
PARTITION BY RANGE (YEAR(attendance_date)) (
    PARTITION p2024 VALUES LESS THAN (2025),
    PARTITION p2025 VALUES LESS THAN (2026),
    PARTITION p2026 VALUES LESS THAN (2027),
    PARTITION p_future VALUES LESS THAN MAXVALUE
);
```

## Payroll Management Tables

### 21. Teacher Salary Components Table (Enhanced)
```sql
CREATE TABLE teacher_salary_components (
    id BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    teacher_id BIGINT UNSIGNED NOT NULL,
    component_type ENUM('base_salary', 'teaching_allowance', 'position_allowance', 'performance_bonus', 
                       'overtime', 'transport', 'meal', 'health', 'other_allowance', 
                       'tax_deduction', 'insurance_deduction', 'loan_deduction', 'other_deduction') NOT NULL,
    component_name VARCHAR(255) NOT NULL,
    amount DECIMAL(15, 2) NOT NULL,
    calculation_method ENUM('fixed', 'percentage', 'hourly', 'daily', 'per_class') DEFAULT 'fixed',
    calculation_base DECIMAL(15, 2) NULL, -- Base amount for percentage calculations
    is_taxable BOOLEAN DEFAULT TRUE,
    is_active BOOLEAN DEFAULT TRUE,
    effective_start_date DATE NOT NULL,
    effective_end_date DATE NULL,
    notes TEXT NULL,
    created_at TIMESTAMP NULL,
    updated_at TIMESTAMP NULL,
    
    INDEX idx_teacher (teacher_id),
    INDEX idx_component_type (component_type),
    INDEX idx_active (is_active),
    INDEX idx_effective_dates (effective_start_date, effective_end_date),
    INDEX idx_teacher_type (teacher_id, component_type),
    CONSTRAINT chk_amount CHECK (amount >= 0),
    CONSTRAINT chk_calculation_base CHECK (calculation_base IS NULL OR calculation_base >= 0),
    FOREIGN KEY (teacher_id) REFERENCES teachers(id) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
```

### 22. Payroll Records Table (Enhanced)
```sql
CREATE TABLE payroll_records (
    id BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    teacher_id BIGINT UNSIGNED NOT NULL,
    academic_year_id BIGINT UNSIGNED NOT NULL,
    payroll_period_start DATE NOT NULL,
    payroll_period_end DATE NOT NULL,
    pay_date DATE NULL,
    
    -- Attendance Summary
    total_working_days SMALLINT UNSIGNED DEFAULT 0,
    total_present_days SMALLINT UNSIGNED DEFAULT 0,
    total_absent_days SMALLINT UNSIGNED DEFAULT 0,
    total_late_days SMALLINT UNSIGNED DEFAULT 0,
    total_overtime_hours DECIMAL(5, 2) DEFAULT 0.00,
    
    -- Salary Components
    base_salary DECIMAL(15, 2) DEFAULT 0.00,
    total_allowances DECIMAL(15, 2) DEFAULT 0.00,
    total_bonuses DECIMAL(15, 2) DEFAULT 0.00,
    overtime_pay DECIMAL(15, 2) DEFAULT 0.00,
    gross_salary DECIMAL(15, 2) DEFAULT 0.00,
    
    -- Deductions
    tax_deduction DECIMAL(15, 2) DEFAULT 0.00,
    insurance_deduction DECIMAL(15, 2) DEFAULT 0.00,
    loan_deduction DECIMAL(15, 2) DEFAULT 0.00,
    other_deductions DECIMAL(15, 2) DEFAULT 0.00,
    total_deductions DECIMAL(15, 2) DEFAULT 0.00,
    
    -- Final Amounts
    net_salary DECIMAL(15, 2) DEFAULT 0.00,
    
    -- Status and Processing
    status ENUM('draft', 'calculated', 'approved', 'paid', 'cancelled') DEFAULT 'draft',
    calculation_notes TEXT NULL,
    payment_method ENUM('bank_transfer', 'cash', 'check') DEFAULT 'bank_transfer',
    bank_account VARCHAR(100) NULL,
    payment_reference VARCHAR(100) NULL,
    
    -- Approval Workflow
    calculated_by BIGINT UNSIGNED NULL,
    calculated_at TIMESTAMP NULL,
    approved_by BIGINT UNSIGNED NULL,
    approved_at TIMESTAMP NULL,
    paid_by BIGINT UNSIGNED NULL,
    paid_at TIMESTAMP NULL,
    
    created_at TIMESTAMP NULL,
    updated_at TIMESTAMP NULL,
    
    INDEX idx_teacher (teacher_id),
    INDEX idx_academic_year (academic_year_id),
    INDEX idx_payroll_period (payroll_period_start, payroll_period_end),
    INDEX idx_pay_date (pay_date),
    INDEX idx_status (status),
    INDEX idx_teacher_period (teacher_id, payroll_period_start, payroll_period_end),
    INDEX idx_calculated_by (calculated_by),
    INDEX idx_approved_by (approved_by),
    INDEX idx_paid_by (paid_by),
    UNIQUE KEY unique_teacher_payroll_period (teacher_id, payroll_period_start, payroll_period_end),
    CONSTRAINT chk_payroll_period CHECK (payroll_period_end >= payroll_period_start),
    CONSTRAINT chk_gross_salary CHECK (gross_salary >= 0),
    CONSTRAINT chk_net_salary CHECK (net_salary >= 0),
    FOREIGN KEY (teacher_id) REFERENCES teachers(id) ON DELETE CASCADE,
    FOREIGN KEY (academic_year_id) REFERENCES academic_years(id) ON DELETE CASCADE,
    FOREIGN KEY (calculated_by) REFERENCES users(id) ON DELETE SET NULL,
    FOREIGN KEY (approved_by) REFERENCES users(id) ON DELETE SET NULL,
    FOREIGN KEY (paid_by) REFERENCES users(id) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
```

## Supporting Tables

### 23. Audit Logs Table (New)
```sql
CREATE TABLE audit_logs (
    id BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    table_name VARCHAR(100) NOT NULL,
    record_id BIGINT UNSIGNED NOT NULL,
    action ENUM('create', 'update', 'delete', 'restore') NOT NULL,
    old_values JSON NULL,
    new_values JSON NULL,
    changed_fields JSON NULL, -- Array of changed field names
    user_id BIGINT UNSIGNED NULL,
    ip_address VARCHAR(45) NULL,
    user_agent TEXT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    INDEX idx_table_record (table_name, record_id),
    INDEX idx_user_date (user_id, created_at),
    INDEX idx_action (action),
    INDEX idx_table_name (table_name),
    INDEX idx_created_at (created_at),
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
PARTITION BY RANGE (YEAR(created_at)) (
    PARTITION p2024 VALUES LESS THAN (2025),
    PARTITION p2025 VALUES LESS THAN (2026),
    PARTITION p2026 VALUES LESS THAN (2027),
    PARTITION p_future VALUES LESS THAN MAXVALUE
);
```

### 24. System Settings Table (New)
```sql
CREATE TABLE system_settings (
    id BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    category VARCHAR(100) NOT NULL,
    setting_key VARCHAR(100) NOT NULL,
    setting_value TEXT NULL,
    data_type ENUM('string', 'integer', 'decimal', 'boolean', 'json', 'date', 'time') DEFAULT 'string',
    description TEXT NULL,
    is_public BOOLEAN DEFAULT FALSE, -- Can be accessed by non-admin users
    is_editable BOOLEAN DEFAULT TRUE,
    validation_rules JSON NULL, -- Validation rules for the setting
    default_value TEXT NULL,
    created_at TIMESTAMP NULL,
    updated_at TIMESTAMP NULL,
    
    INDEX idx_category (category),
    INDEX idx_setting_key (setting_key),
    INDEX idx_public (is_public),
    INDEX idx_editable (is_editable),
    UNIQUE KEY unique_category_key (category, setting_key)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
```

### 25. Notification Templates Table (New)
```sql
CREATE TABLE notification_templates (
    id BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(255) NOT NULL,
    code VARCHAR(100) UNIQUE NOT NULL,
    category ENUM('attendance', 'academic', 'payroll', 'system', 'emergency') NOT NULL,
    channel ENUM('email', 'sms', 'push', 'whatsapp', 'in_app') NOT NULL,
    subject VARCHAR(500) NULL, -- For email notifications
    template_body TEXT NOT NULL,
    variables JSON NULL, -- Available template variables
    is_active BOOLEAN DEFAULT TRUE,
    language VARCHAR(10) DEFAULT 'id',
    created_at TIMESTAMP NULL,
    updated_at TIMESTAMP NULL,
    
    INDEX idx_code (code),
    INDEX idx_category (category),
    INDEX idx_channel (channel),
    INDEX idx_active (is_active),
    INDEX idx_language (language)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
```
