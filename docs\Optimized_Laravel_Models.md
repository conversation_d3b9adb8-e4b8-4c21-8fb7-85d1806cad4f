# Optimized Laravel Models

This document contains the optimized Laravel Eloquent models that correspond to the enhanced database schema.

## Core Models

### 1. User Model (Enhanced)

```php
<?php
// app/Models/User.php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Foundation\Auth\User as Authenticatable;
use Illuminate\Notifications\Notifiable;
use Spatie\Permission\Traits\HasRoles;

class User extends Authenticatable
{
    use HasFactory, Notifiable, HasRoles, SoftDeletes;

    protected $fillable = [
        'username',
        'name',
        'email',
        'photo_path',
        'password',
        'email_verified_at',
        'status',
        'last_login_at',
        'login_attempts',
        'locked_until',
    ];

    protected $hidden = [
        'password',
        'remember_token',
    ];

    protected $casts = [
        'email_verified_at' => 'datetime',
        'last_login_at' => 'datetime',
        'locked_until' => 'datetime',
        'status' => 'boolean',
        'login_attempts' => 'integer',
        'password' => 'hashed',
    ];

    // Relationships
    public function teacher()
    {
        return $this->hasOne(Teacher::class);
    }

    public function parent()
    {
        return $this->hasOne(ParentModel::class);
    }

    public function student()
    {
        return $this->hasOne(Student::class);
    }

    public function contactInformation()
    {
        return $this->morphMany(ContactInformation::class, 'entity');
    }

    public function primaryContact()
    {
        return $this->morphOne(ContactInformation::class, 'entity')
                    ->where('is_primary', true);
    }

    // Scopes
    public function scopeActive($query)
    {
        return $query->where('status', true);
    }

    public function scopeByRole($query, $role)
    {
        return $query->whereHas('roles', function ($q) use ($role) {
            $q->where('name', $role);
        });
    }

    // Accessors & Mutators
    public function getIsLockedAttribute()
    {
        return $this->locked_until && $this->locked_until->isFuture();
    }

    public function getCanLoginAttribute()
    {
        return $this->status && !$this->is_locked;
    }
}
```

### 2. Contact Information Model (New)

```php
<?php
// app/Models/ContactInformation.php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class ContactInformation extends Model
{
    use HasFactory;

    protected $table = 'contact_information';

    protected $fillable = [
        'entity_type',
        'entity_id',
        'phone_primary',
        'phone_secondary',
        'email_secondary',
        'address_line_1',
        'address_line_2',
        'city',
        'state',
        'postal_code',
        'country',
        'is_primary',
    ];

    protected $casts = [
        'is_primary' => 'boolean',
    ];

    // Polymorphic relationship
    public function entity()
    {
        return $this->morphTo();
    }

    // Scopes
    public function scopePrimary($query)
    {
        return $query->where('is_primary', true);
    }

    public function scopeByEntityType($query, $type)
    {
        return $query->where('entity_type', $type);
    }

    // Accessors
    public function getFullAddressAttribute()
    {
        $parts = array_filter([
            $this->address_line_1,
            $this->address_line_2,
            $this->city,
            $this->state,
            $this->postal_code,
            $this->country,
        ]);

        return implode(', ', $parts);
    }
}
```

### 3. Teacher Model (Enhanced)

```php
<?php
// app/Models/Teacher.php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use App\Enums\Gender;
use App\Enums\EducationLevel;
use App\Enums\EmploymentStatus;
use App\Enums\TeacherStatus;

class Teacher extends Model
{
    use HasFactory, SoftDeletes;

    protected $fillable = [
        'user_id',
        'employee_id',
        'nip',
        'birth_place',
        'birth_date',
        'gender',
        'education_level',
        'specialization',
        'hire_date',
        'employment_status',
        'gps_consent',
        'gps_consent_given_at',
        'emergency_contact_name',
        'emergency_contact_phone',
        'status',
    ];

    protected $casts = [
        'birth_date' => 'date',
        'hire_date' => 'date',
        'gps_consent' => 'boolean',
        'gps_consent_given_at' => 'datetime',
        'gender' => Gender::class,
        'education_level' => EducationLevel::class,
        'employment_status' => EmploymentStatus::class,
        'status' => TeacherStatus::class,
    ];

    // Relationships
    public function user()
    {
        return $this->belongsTo(User::class);
    }

    public function contactInformation()
    {
        return $this->morphMany(ContactInformation::class, 'entity');
    }

    public function assignments()
    {
        return $this->hasMany(TeacherAssignment::class);
    }

    public function attendances()
    {
        return $this->hasMany(TeacherAttendance::class);
    }

    public function salaryComponents()
    {
        return $this->hasMany(TeacherSalaryComponent::class);
    }

    public function payrollRecords()
    {
        return $this->hasMany(PayrollRecord::class);
    }

    public function homeroomClassrooms()
    {
        return $this->hasMany(Classroom::class, 'homeroom_teacher_id');
    }

    public function geofenceLogs()
    {
        return $this->hasMany(GeofenceLog::class);
    }

    // Scopes
    public function scopeActive($query)
    {
        return $query->where('status', TeacherStatus::ACTIVE);
    }

    public function scopeByEmploymentStatus($query, $status)
    {
        return $query->where('employment_status', $status);
    }

    public function scopeWithGpsConsent($query)
    {
        return $query->where('gps_consent', true);
    }

    public function scopeInAcademicYear($query, $academicYearId)
    {
        return $query->whereHas('assignments', function ($q) use ($academicYearId) {
            $q->where('academic_year_id', $academicYearId)
              ->where('status', 'active');
        });
    }

    // Accessors
    public function getFullNameAttribute()
    {
        return $this->user->name;
    }

    public function getAgeAttribute()
    {
        return $this->birth_date?->age;
    }

    public function getYearsOfServiceAttribute()
    {
        return $this->hire_date?->diffInYears(now());
    }

    public function getCurrentWorkloadAttribute()
    {
        return $this->assignments()
                    ->where('status', 'active')
                    ->whereHas('academicYear', function ($q) {
                        $q->where('status', 'active');
                    })
                    ->sum('weekly_hours');
    }
}
```

### 4. Student Model (Enhanced)

```php
<?php
// app/Models/Student.php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use App\Enums\Gender;
use App\Enums\Religion;
use App\Enums\BloodType;
use App\Enums\StudentStatus;

class Student extends Model
{
    use HasFactory, SoftDeletes;

    protected $fillable = [
        'user_id',
        'nis',
        'nisn',
        'student_code',
        'birth_place',
        'birth_date',
        'gender',
        'religion',
        'blood_type',
        'entry_year',
        'entry_date',
        'graduation_date',
        'profile_picture',
        'medical_conditions',
        'allergies',
        'special_needs',
        'status',
    ];

    protected $casts = [
        'birth_date' => 'date',
        'entry_date' => 'date',
        'graduation_date' => 'date',
        'entry_year' => 'integer',
        'gender' => Gender::class,
        'religion' => Religion::class,
        'blood_type' => BloodType::class,
        'status' => StudentStatus::class,
    ];

    // Relationships
    public function user()
    {
        return $this->belongsTo(User::class);
    }

    public function contactInformation()
    {
        return $this->morphMany(ContactInformation::class, 'entity');
    }

    public function parents()
    {
        return $this->belongsToMany(ParentModel::class, 'student_parents')
                    ->withPivot([
                        'relation_type',
                        'is_primary_contact',
                        'is_emergency_contact',
                        'financial_responsibility',
                        'custody_rights'
                    ])
                    ->withTimestamps();
    }

    public function classroomStudents()
    {
        return $this->hasMany(ClassroomStudent::class);
    }

    public function attendances()
    {
        return $this->hasMany(StudentAttendance::class);
    }

    // Current classroom relationship
    public function currentClassroom()
    {
        return $this->belongsToMany(Classroom::class, 'classroom_students')
                    ->withPivot(['enrollment_date', 'seat_number', 'status'])
                    ->wherePivot('status', 'enrolled')
                    ->whereHas('academicYear', function ($q) {
                        $q->where('status', 'active');
                    });
    }

    // Scopes
    public function scopeActive($query)
    {
        return $query->where('status', StudentStatus::ACTIVE);
    }

    public function scopeInGrade($query, $level)
    {
        return $query->whereHas('currentClassroom', function ($q) use ($level) {
            $q->where('level', $level);
        });
    }

    public function scopeInAcademicYear($query, $academicYearId)
    {
        return $query->whereHas('classroomStudents', function ($q) use ($academicYearId) {
            $q->where('academic_year_id', $academicYearId)
              ->where('status', 'enrolled');
        });
    }

    // Accessors
    public function getFullNameAttribute()
    {
        return $this->user->name;
    }

    public function getAgeAttribute()
    {
        return $this->birth_date?->age;
    }

    public function getCurrentGradeAttribute()
    {
        return $this->currentClassroom->first()?->level;
    }

    public function getPrimaryParentAttribute()
    {
        return $this->parents()
                    ->wherePivot('is_primary_contact', true)
                    ->first();
    }
}
```
