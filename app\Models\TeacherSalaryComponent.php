<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class TeacherSalaryComponent extends Model
{
    protected $fillable = [
        'teacher_id',
        'component_name',
        'component_type',
        'amount',
        'is_percentage',
        'is_taxable',
        'effective_date',
        'end_date',
        'is_active',
        'description'
    ];

    protected $casts = [
        'amount' => 'decimal:2',
        'is_percentage' => 'boolean',
        'is_taxable' => 'boolean',
        'is_active' => 'boolean',
        'effective_date' => 'date',
        'end_date' => 'date',
    ];

    public function teacher(): BelongsTo
    {
        return $this->belongsTo(Teacher::class);
    }

    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    public function scopeAllowances($query)
    {
        return $query->where('component_type', 'allowance');
    }

    public function scopeDeductions($query)
    {
        return $query->where('component_type', 'deduction');
    }
}
