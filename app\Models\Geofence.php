<?php

namespace App\Models;

use App\Enums\GeneralStatus;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;

class Geofence extends Model
{
    protected $fillable = [
        'name',
        'description',
        'center_latitude',
        'center_longitude',
        'radius_meters',
        'status'
    ];

    protected $casts = [
        'center_latitude' => 'decimal:8',
        'center_longitude' => 'decimal:8',
        'radius_meters' => 'integer',
        'status' => GeneralStatus::class,
    ];

    public function geofenceLogs(): HasMany
    {
        return $this->hasMany(GeofenceLog::class);
    }

    public function scopeActive($query)
    {
        return $query->where('status', GeneralStatus::Active);
    }

    public function isWithinRadius($latitude, $longitude): bool
    {
        $earthRadius = 6371000; // Earth's radius in meters

        $latDiff = deg2rad($latitude - $this->center_latitude);
        $lonDiff = deg2rad($longitude - $this->center_longitude);

        $a = sin($latDiff / 2) * sin($latDiff / 2) +
            cos(deg2rad($this->center_latitude)) * cos(deg2rad($latitude)) *
            sin($lonDiff / 2) * sin($lonDiff / 2);

        $c = 2 * atan2(sqrt($a), sqrt(1 - $a));
        $distance = $earthRadius * $c;

        return $distance <= $this->radius_meters;
    }
}
