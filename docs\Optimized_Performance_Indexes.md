# Performance Optimization - Indexes and Views

This document contains additional performance optimizations including composite indexes, database views, and triggers for the optimized schema.

## Critical Performance Indexes

### 1. Attendance Performance Indexes

```sql
-- Teacher attendance performance indexes
ALTER TABLE teacher_attendances 
ADD INDEX idx_teacher_date_status (teacher_id, attendance_date, status),
ADD INDEX idx_date_status_performance (attendance_date, status),
ADD INDEX idx_academic_year_date (academic_year_id, attendance_date),
ADD INDEX idx_late_early_tracking (late_minutes, early_leave_minutes);

-- Student attendance performance indexes  
ALTER TABLE student_attendances
ADD INDEX idx_student_date_status (student_id, attendance_date, attendance_status),
ADD INDEX idx_classroom_date_status (classroom_id, attendance_date, attendance_status),
ADD INDEX idx_subject_date_performance (subject_id, attendance_date),
ADD INDEX idx_parent_notification (parent_notified, parent_notified_at),
ADD INDEX idx_follow_up_tracking (follow_up_required, attendance_date);
```

### 2. Scheduling Performance Indexes

```sql
-- Class schedules performance indexes
ALTER TABLE class_schedules
ADD INDEX idx_day_lesson_performance (day_of_week, lesson_hour_id, status),
ADD INDEX idx_teacher_day_schedule (teacher_assignment_id, day_of_week),
ADD INDEX idx_academic_year_active (academic_year_id, status),
ADD INDEX idx_effective_date_range (effective_start_date, effective_end_date, status);

-- Teacher assignments performance indexes
ALTER TABLE teacher_assignments
ADD INDEX idx_teacher_academic_year (teacher_id, academic_year_id, status),
ADD INDEX idx_classroom_subject_year (classroom_id, subject_id, academic_year_id),
ADD INDEX idx_homeroom_teachers (is_homeroom_teacher, academic_year_id),
ADD INDEX idx_assignment_type_status (assignment_type, status);
```

### 3. Payroll Performance Indexes

```sql
-- Payroll records performance indexes
ALTER TABLE payroll_records
ADD INDEX idx_teacher_period_status (teacher_id, payroll_period_start, payroll_period_end, status),
ADD INDEX idx_pay_date_status (pay_date, status),
ADD INDEX idx_approval_workflow (status, approved_at, paid_at),
ADD INDEX idx_academic_year_period (academic_year_id, payroll_period_start);

-- Salary components performance indexes
ALTER TABLE teacher_salary_components
ADD INDEX idx_teacher_type_active (teacher_id, component_type, is_active),
ADD INDEX idx_effective_date_active (effective_start_date, effective_end_date, is_active),
ADD INDEX idx_component_calculation (component_type, calculation_method);
```

## Database Views for Reporting

### 1. Student Attendance Summary View

```sql
CREATE VIEW student_attendance_summary AS
SELECT 
    s.id as student_id,
    s.nis,
    u.name as student_name,
    c.name as classroom_name,
    c.level as grade_level,
    p.name as program_name,
    ay.name as academic_year,
    COUNT(sa.id) as total_sessions,
    SUM(CASE WHEN sa.attendance_status = 'present' THEN 1 ELSE 0 END) as present_count,
    SUM(CASE WHEN sa.attendance_status = 'absent' THEN 1 ELSE 0 END) as absent_count,
    SUM(CASE WHEN sa.attendance_status = 'late' THEN 1 ELSE 0 END) as late_count,
    SUM(CASE WHEN sa.attendance_status = 'sick' THEN 1 ELSE 0 END) as sick_count,
    SUM(CASE WHEN sa.attendance_status = 'permission' THEN 1 ELSE 0 END) as permission_count,
    SUM(CASE WHEN sa.attendance_status = 'alpha' THEN 1 ELSE 0 END) as alpha_count,
    ROUND((SUM(CASE WHEN sa.attendance_status = 'present' THEN 1 ELSE 0 END) / COUNT(sa.id)) * 100, 2) as attendance_percentage,
    MAX(sa.attendance_date) as last_attendance_date
FROM students s
JOIN users u ON s.user_id = u.id
JOIN classroom_students cs ON s.id = cs.student_id
JOIN classrooms c ON cs.classroom_id = c.id
JOIN programs p ON c.program_id = p.id
JOIN academic_years ay ON cs.academic_year_id = ay.id
LEFT JOIN student_attendances sa ON s.id = sa.student_id
WHERE s.deleted_at IS NULL
GROUP BY s.id, c.id, ay.id;
```

### 2. Teacher Workload Summary View

```sql
CREATE VIEW teacher_workload_summary AS
SELECT 
    t.id as teacher_id,
    t.employee_id,
    u.name as teacher_name,
    ay.name as academic_year,
    COUNT(DISTINCT ta.classroom_id) as total_classrooms,
    COUNT(DISTINCT ta.subject_id) as total_subjects,
    SUM(ta.weekly_hours) as total_weekly_hours,
    COUNT(CASE WHEN ta.is_homeroom_teacher = 1 THEN 1 END) as homeroom_classes,
    COUNT(DISTINCT cs.id) as total_schedules,
    GROUP_CONCAT(DISTINCT sub.name ORDER BY sub.name SEPARATOR ', ') as subjects_taught,
    GROUP_CONCAT(DISTINCT cl.name ORDER BY cl.name SEPARATOR ', ') as classrooms_assigned
FROM teachers t
JOIN users u ON t.user_id = u.id
JOIN teacher_assignments ta ON t.id = ta.teacher_id
JOIN academic_years ay ON ta.academic_year_id = ay.id
JOIN subjects sub ON ta.subject_id = sub.id
JOIN classrooms cl ON ta.classroom_id = cl.id
LEFT JOIN class_schedules cs ON ta.id = cs.teacher_assignment_id
WHERE t.deleted_at IS NULL 
  AND ta.status = 'active'
  AND ay.status = 'active'
GROUP BY t.id, ay.id;
```

### 3. Classroom Capacity Utilization View

```sql
CREATE VIEW classroom_capacity_utilization AS
SELECT 
    c.id as classroom_id,
    c.name as classroom_name,
    c.code as classroom_code,
    c.level as grade_level,
    p.name as program_name,
    ay.name as academic_year,
    c.capacity as max_capacity,
    COUNT(cs.student_id) as current_students,
    (c.capacity - COUNT(cs.student_id)) as available_spots,
    ROUND((COUNT(cs.student_id) / c.capacity) * 100, 2) as utilization_percentage,
    CASE 
        WHEN COUNT(cs.student_id) >= c.capacity THEN 'Full'
        WHEN COUNT(cs.student_id) >= (c.capacity * 0.9) THEN 'Near Full'
        WHEN COUNT(cs.student_id) >= (c.capacity * 0.7) THEN 'Moderate'
        ELSE 'Low'
    END as utilization_status
FROM classrooms c
JOIN programs p ON c.program_id = p.id
JOIN academic_years ay ON c.academic_year_id = ay.id
LEFT JOIN classroom_students cs ON c.id = cs.classroom_id 
    AND cs.academic_year_id = c.academic_year_id 
    AND cs.status = 'enrolled'
WHERE c.deleted_at IS NULL
  AND c.status = 'active'
GROUP BY c.id, ay.id;
```

### 4. Teacher Attendance Performance View

```sql
CREATE VIEW teacher_attendance_performance AS
SELECT 
    t.id as teacher_id,
    t.employee_id,
    u.name as teacher_name,
    ay.name as academic_year,
    DATE_FORMAT(ta.attendance_date, '%Y-%m') as month_year,
    COUNT(ta.id) as total_scheduled_days,
    SUM(CASE WHEN ta.status = 'present' THEN 1 ELSE 0 END) as present_days,
    SUM(CASE WHEN ta.status = 'absent' THEN 1 ELSE 0 END) as absent_days,
    SUM(CASE WHEN ta.status = 'late' THEN 1 ELSE 0 END) as late_days,
    SUM(CASE WHEN ta.status IN ('sick', 'permission') THEN 1 ELSE 0 END) as excused_days,
    SUM(ta.late_minutes) as total_late_minutes,
    SUM(ta.overtime_minutes) as total_overtime_minutes,
    ROUND((SUM(CASE WHEN ta.status = 'present' THEN 1 ELSE 0 END) / COUNT(ta.id)) * 100, 2) as attendance_percentage,
    AVG(CASE WHEN ta.status = 'late' AND ta.late_minutes > 0 THEN ta.late_minutes END) as avg_late_minutes
FROM teachers t
JOIN users u ON t.user_id = u.id
JOIN teacher_attendances ta ON t.id = ta.teacher_id
JOIN academic_years ay ON ta.academic_year_id = ay.id
WHERE t.deleted_at IS NULL
GROUP BY t.id, ay.id, DATE_FORMAT(ta.attendance_date, '%Y-%m');
```

## Database Triggers for Data Integrity

### 1. Classroom Capacity Trigger

```sql
DELIMITER //

CREATE TRIGGER check_classroom_capacity_before_insert
BEFORE INSERT ON classroom_students
FOR EACH ROW
BEGIN
    DECLARE current_count INT;
    DECLARE max_capacity INT;
    
    SELECT COUNT(*), c.capacity 
    INTO current_count, max_capacity
    FROM classroom_students cs
    JOIN classrooms c ON cs.classroom_id = c.id
    WHERE cs.classroom_id = NEW.classroom_id 
      AND cs.academic_year_id = NEW.academic_year_id
      AND cs.status = 'enrolled'
    GROUP BY c.capacity;
    
    IF current_count >= max_capacity THEN
        SIGNAL SQLSTATE '45000' 
        SET MESSAGE_TEXT = 'Classroom capacity exceeded. Cannot enroll more students.';
    END IF;
END//

CREATE TRIGGER update_classroom_student_count_after_insert
AFTER INSERT ON classroom_students
FOR EACH ROW
BEGIN
    UPDATE classrooms 
    SET current_students = (
        SELECT COUNT(*) 
        FROM classroom_students 
        WHERE classroom_id = NEW.classroom_id 
          AND academic_year_id = NEW.academic_year_id
          AND status = 'enrolled'
    )
    WHERE id = NEW.classroom_id;
END//

CREATE TRIGGER update_classroom_student_count_after_update
AFTER UPDATE ON classroom_students
FOR EACH ROW
BEGIN
    UPDATE classrooms 
    SET current_students = (
        SELECT COUNT(*) 
        FROM classroom_students 
        WHERE classroom_id = NEW.classroom_id 
          AND academic_year_id = NEW.academic_year_id
          AND status = 'enrolled'
    )
    WHERE id = NEW.classroom_id;
    
    -- Update old classroom if classroom changed
    IF OLD.classroom_id != NEW.classroom_id THEN
        UPDATE classrooms 
        SET current_students = (
            SELECT COUNT(*) 
            FROM classroom_students 
            WHERE classroom_id = OLD.classroom_id 
              AND academic_year_id = OLD.academic_year_id
              AND status = 'enrolled'
        )
        WHERE id = OLD.classroom_id;
    END IF;
END//

DELIMITER ;
```

### 2. Payroll Calculation Trigger

```sql
DELIMITER //

CREATE TRIGGER calculate_payroll_totals_before_update
BEFORE UPDATE ON payroll_records
FOR EACH ROW
BEGIN
    -- Calculate gross salary
    SET NEW.gross_salary = NEW.base_salary + NEW.total_allowances + NEW.total_bonuses + NEW.overtime_pay;
    
    -- Calculate total deductions
    SET NEW.total_deductions = NEW.tax_deduction + NEW.insurance_deduction + NEW.loan_deduction + NEW.other_deductions;
    
    -- Calculate net salary
    SET NEW.net_salary = NEW.gross_salary - NEW.total_deductions;
    
    -- Ensure net salary is not negative
    IF NEW.net_salary < 0 THEN
        SET NEW.net_salary = 0;
    END IF;
END//

DELIMITER ;
```
