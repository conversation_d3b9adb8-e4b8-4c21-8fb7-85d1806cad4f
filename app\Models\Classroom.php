<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use Illuminate\Database\Eloquent\Relations\HasMany;

class Classroom extends Model
{
    protected $fillable = [
        'academic_year_id',
        'name',
        'grade_level',
        'capacity',
        'location',
        'notes'
    ];

    protected $casts = [
        'capacity' => 'integer',
        'grade_level' => 'integer',
    ];

    public function academicYear(): BelongsTo
    {
        return $this->belongsTo(AcademicYear::class);
    }

    public function students(): BelongsToMany
    {
        return $this->belongsToMany(Student::class, 'classroom_students')
            ->withPivot('enrollment_date', 'completion_date', 'is_active', 'notes')
            ->withTimestamps();
    }

    public function teacherAssignments(): Has<PERSON><PERSON>
    {
        return $this->hasMany(TeacherAssignment::class);
    }

    public function activeStudents(): BelongsToMany
    {
        return $this->students()->wherePivot('is_active', true);
    }
}
