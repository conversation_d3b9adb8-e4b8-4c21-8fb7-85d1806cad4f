# Laravel Migrations and Models for `db_rawooh_v2`

Below are the migration and model files for the `db_rawooh_v2` database, excluding <PERSON><PERSON>'s default tables. The migrations create the tables with the specified schema, and the models define the Eloquent ORM structure, including fillable fields and relationships.

---

## Migrations

### 1. `create_users_table.php`
```php
<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::create('users', function (Blueprint $table) {
            $table->id();
            $table->string('username')->unique();
            $table->string('name');
            $table->string('email')->unique();
            $table->string('photo_path')->nullable();
            $table->string('password');
            $table->timestamp('email_verified_at')->nullable();
            $table->string('phone_number', 20)->nullable();
            $table->boolean('status')->default(1);
            $table->timestamp('last_login_at')->nullable();
            $table->string('remember_token', 100)->nullable();
            $table->timestamps();
            $table->index('email', 'users_email_index');
        });
    }

    public function down(): void
    {
        Schema::dropIfExists('users');
    }
};
```

### 2. `create_password_reset_tokens_table.php`
```php
<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::create('password_reset_tokens', function (Blueprint $table) {
            $table->string('email')->primary();
            $table->string('token');
            $table->timestamp('created_at')->nullable();
        });
    }

    public function down(): void
    {
        Schema::dropIfExists('password_reset_tokens');
    }
};
```

### 3. `create_sessions_table.php`
```php
<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::create('sessions', function (Blueprint $table) {
            $table->string('id')->primary();
            $table->foreignId('user_id')->nullable()->constrained()->onDelete('cascade');
            $table->string('ip_address', 45)->nullable();
            $table->text('user_agent')->nullable();
            $table->longText('payload');
            $table->integer('last_activity');
            $table->index('user_id', 'sessions_user_id_index');
            $table->index('last_activity', 'sessions_last_activity_index');
        });
    }

    public function down(): void
    {
        Schema::dropIfExists('sessions');
    }
};
```

### 4. `create_academic_years_table.php`
```php
<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::create('academic_years', function (Blueprint $table) {
            $table->id();
            $table->string('name');
            $table->enum('semester', ['odd', 'even']);
            $table->date('start_date');
            $table->date('end_date');
            $table->enum('status', ['planned', 'active', 'completed'])->default('planned');
            $table->timestamps();
            $table->index('status', 'academic_years_status_index');
        });
    }

    public function down(): void
    {
        Schema::dropIfExists('academic_years');
    }
};
```

### 5. `create_programs_table.php`
```php
<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::create('programs', function (Blueprint $table) {
            $table->id();
            $table->string('name');
            $table->string('code')->unique();
            $table->text('description')->nullable();
            $table->enum('status', ['active', 'inactive'])->default('active');
            $table->timestamps();
            $table->index('status', 'programs_status_index');
        });
    }

    public function down(): void
    {
        Schema::dropIfExists('programs');
    }
};
```

### 6. `create_shifts_table.php`
```php
<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::create('shifts', function (Blueprint $table) {
            $table->id();
            $table->string('name');
            $table->text('description')->nullable();
            $table->enum('status', ['active', 'inactive'])->default('active');
            $table->timestamps();
            $table->index('status', 'shifts_status_index');
        });
    }

    public function down(): void
    {
        Schema::dropIfExists('shifts');
    }
};
```

### 7. `create_classrooms_table.php`
```php
<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::create('classrooms', function (Blueprint $table) {
            $table->id();
            $table->string('name');
            $table->enum('level', ['7', '8', '9']);
            $table->integer('capacity');
            $table->foreignId('program_id')->constrained();
            $table->foreignId('shift_id')->nullable()->constrained()->onDelete('set null');
            $table->foreignId('teacher_id')->nullable()->constrained('teachers')->onDelete('set null');
            $table->foreignId('academic_year_id')->constrained();
            $table->enum('status', ['active', 'inactive'])->default('active');
            $table->text('description')->nullable();
            $table->timestamps();
            $table->index('program_id', 'classrooms_program_id_foreign');
            $table->index('teacher_id', 'classrooms_teacher_id_foreign');
            $table->index('academic_year_id', 'classrooms_academic_year_id_foreign');
            $table->index('shift_id', 'classrooms_shift_id_foreign');
            $table->index('status', 'classrooms_status_index');
        });
    }

    public function down(): void
    {
        Schema::dropIfExists('classrooms');
    }
};
```

### 8. `create_subjects_table.php`
```php
<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::create('subjects', function (Blueprint $table) {
            $table->id();
            $table->string('name');
            $table->foreignId('program_id')->constrained();
            $table->timestamps();
            $table->index('program_id', 'subjects_program_id_foreign');
        });
    }

    public function down(): void
    {
        Schema::dropIfExists('subjects');
    }
};
```

### 9. `create_lesson_hours_table.php`
```php
<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::create('lesson_hours', function (Blueprint $table) {
            $table->id();
            $table->string('name');
            $table->time('start_time');
            $table->time('end_time');
            $table->integer('sequence');
            $table->timestamps();
            $table->unique(['start_time', 'end_time'], 'lesson_hours_unique_time');
        });
    }

    public function down(): void
    {
        Schema::dropIfExists('lesson_hours');
    }
};
```

### 10. `create_teachers_table.php`
```php
<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::create('teachers', function (Blueprint $table) {
            $table->id();
            $table->foreignId('user_id')->unique()->constrained()->onDelete('cascade');
            $table->string('nip')->nullable()->unique();
            $table->string('birth_place');
            $table->date('birth_date');
            $table->enum('gender', ['male', 'female']);
            $table->string('phone_number', 20);
            $table->text('full_address');
            $table->boolean('gps_consent')->default(0);
            $table->timestamp('gps_consent_given_at')->nullable();
            $table->timestamps();
            $table->index('user_id', 'teachers_user_id_foreign');
        });
    }

    public function down(): void
    {
        Schema::dropIfExists('teachers');
    }
};
```

### 11. `create_parents_table.php`
```php
<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::create('parents', function (Blueprint $table) {
            $table->id();
            $table->foreignId('user_id')->nullable()->constrained()->onDelete('set null');
            $table->string('name');
            $table->string('phone_number', 15);
            $table->string('occupation', 100)->nullable();
            $table->text('address')->nullable();
            $table->timestamps();
            $table->index('user_id', 'parents_user_id_foreign');
        });
    }

    public function down(): void
    {
        Schema::dropIfExists('parents');
    }
};
```

### 12. `create_students_table.php`
```php
<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::create('students', function (Blueprint $table) {
            $table->id();
            $table->foreignId('user_id')->unique()->constrained()->onDelete('cascade');
            $table->string('nis', 20)->unique();
            $table->string('nisn', 20)->unique();
            $table->string('birth_place');
            $table->date('birth_date');
            $table->enum('gender', ['male', 'female']);
            $table->string('religion', 20);
            $table->text('address');
            $table->year('entry_year');
            $table->string('profile_picture')->nullable();
            $table->string('phone', 20)->nullable();
            $table->timestamps();
            $table->index('user_id', 'students_user_id_foreign');
            $table->index('nis', 'students_nis_index');
            $table->index('nisn', 'students_nisn_index');
        });
    }

    public function down(): void
    {
        Schema::dropIfExists('students');
    }
};
```

### 13. `create_student_parents_table.php`
```php
<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::create('student_parents', function (Blueprint $table) {
            $table->id();
            $table->foreignId('student_id')->constrained()->onDelete('cascade');
            $table->foreignId('parent_id')->constrained()->onDelete('cascade');
            $table->string('relation_type', 50)->nullable();
            $table->timestamps();
            $table->unique(['student_id', 'parent_id'], 'student_parent_unique');
            $table->index('student_id', 'student_parents_student_id_foreign');
            $table->index('parent_id', 'student_parents_parent_id_foreign');
        });
    }

    public function down(): void
    {
        Schema::dropIfExists('student_parents');
    }
};
```

### 14. `create_classroom_students_table.php`
```php
<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::create('classroom_students', function (Blueprint $table) {
            $table->id();
            $table->foreignId('classroom_id')->constrained()->onDelete('cascade');
            $table->foreignId('student_id')->constrained()->onDelete('cascade');
            $table->foreignId('academic_year_id')->constrained();
            $table->timestamps();
            $table->unique(['classroom_id', 'student_id', 'academic_year_id'], 'classroom_students_unique');
            $table->index('classroom_id', 'classroom_students_classroom_id_foreign');
            $table->index('student_id', 'classroom_students_student_id_foreign');
            $table->index('academic_year_id', 'classroom_students_academic_year_id_foreign');
        });
    }

    public function down(): void
    {
        Schema::dropIfExists('classroom_students');
    }
};
```

### 15. `create_teacher_assignments_table.php`
```php
<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::create('teacher_assignments', function (Blueprint $table) {
            $table->id();
            $table->foreignId('teacher_id')->constrained()->onDelete('cascade');
            $table->foreignId('subject_id')->constrained();
            $table->foreignId('classroom_id')->constrained()->onDelete('cascade');
            $table->foreignId('academic_year_id')->constrained();
            $table->boolean('is_homeroom_teacher')->default(0);
            $table->timestamps();
            $table->unique(['teacher_id', 'subject_id', 'classroom_id', 'academic_year_id'], 'teacher_assignments_unique');
            $table->index('teacher_id', 'teacher_assignments_teacher_id_foreign');
            $table->index('subject_id', 'teacher_assignments_subject_id_foreign');
            $table->index('classroom_id', 'teacher_assignments_classroom_id_foreign');
            $table->index('academic_year_id', 'teacher_assignments_academic_year_id_foreign');
        });
    }

    public function down(): void
    {
        Schema::dropIfExists('teacher_assignments');
    }
};
```

### 16. `create_class_schedules_table.php`
```php
<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema survives class extends Migration
{
    public function up(): void
    {
        Schema::create('class_schedules', function (Blueprint $table) {
            $table->id();
            $table->foreignId('teacher_assignment_id')->constrained()->onDelete('cascade');
            $table->foreignId('lesson_hour_id')->constrained();
            $table->enum('day_of_week', ['monday', 'tuesday', 'wednesday', 'thursday', 'friday', 'saturday', 'sunday']);
            $table->foreignId('academic_year_id')->constrained();
            $table->timestamps();
            $table->unique(['teacher_assignment_id', 'lesson_hour_id', 'day_of_week', 'academic_year_id'], 'class_schedules_unique');
            $table->index('lesson_hour_id', 'class_schedules_lesson_hour_id_foreign');
            $table->index('academic_year_id', 'class_schedules_academic_year_id_foreign');
            $table->index('day_of_week', 'class_schedules_day_of_week_index');
        });
    }

    public function down(): void
    {
        Schema::dropIfExists('class_schedules');
    }
};
```

### 17. `create_schedule_exceptions_table.php`
```php
<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::create('schedule_exceptions', function (Blueprint $table) {
            $table->id();
            $table->foreignId('class_schedule_id')->nullable()->constrained()->onDelete('cascade');
            $table->date('exception_date');
            $table->string('reason');
            $table->timestamps();
            $table->unique(['class_schedule_id', 'exception_date'], 'schedule_exceptions_unique');
            $table->index('exception_date', 'schedule_exceptions_date_index');
        });
    }

    public function down(): void
    {
        Schema::dropIfExists('schedule_exceptions');
    }
};
```

### 18. `create_geofences_table.php`
```php
<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::create('geofences', function (Blueprint $table) {
            $table->id();
            $table->string('name');
            $table->decimal('latitude', 10, 8);
            $table->decimal('longitude', 10, 8);
            $table->decimal('radius', 8, 2);
            $table->timestamps();
        });
    }

    public function down(): void
    {
        Schema::dropIfExists('geofences');
    }
};
```

### 19. `create_teacher_attendances_table.php`
```php
<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::create('teacher_attendances', function (Blueprint $table) {
            $table->id();
            $table->foreignId('teacher_id')->constrained()->onDelete('cascade');
            $table->foreignId('class_schedule_id')->constrained()->onDelete('cascade');
            $table->date('attendance_date');
            $table->dateTime('check_in_time');
            $table->dateTime('check_out_time')->nullable();
            $table->decimal('latitude', 10, 8);
            $table->decimal('longitude', 10, 8);
            $table->foreignId('geofence_id')->nullable()->constrained()->onDelete('set null');
            $table->enum('status', ['present', 'absent', 'late', 'substitute']);
            $table->boolean('is_substitute_teacher')->default(0);
            $table->foreignId('original_teacher_id')->nullable()->constrained('teachers')->onDelete('set null');
            $table->text('notes')->nullable();
            $table->timestamps();
            $table->unique(['teacher_id', 'class_schedule_id', 'attendance_date'], 'teacher_attendance_unique');
            $table->index('teacher_id', 'teacher_attendances_teacher_id_foreign');
            $table->index('class_schedule_id', 'teacher_attendances_class_schedule_id_foreign');
            $table->index('original_teacher_id', 'teacher_attendances_original_teacher_id_foreign');
            $table->index('geofence_id', 'teacher_attendances_geofence_id_foreign');
            $table->index('attendance_date', 'teacher_attendances_attendance_date_index');
            $table->index('status', 'teacher_attendances_status_index');
        });
    }

    public function down(): void
    {
        Schema::dropIfExists('teacher_attendances');
    }
};
```

### 20. `create_student_attendances_table.php`
```php
<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::create('student_attendances', function (Blueprint $table) {
            $table->id();
            $table->foreignId('student_id')->constrained()->onDelete('cascade');
            $table->foreignId('teacher_attendance_id')->constrained()->onDelete('cascade');
            $table->enum('attendance_status', ['present', 'absent', 'sick', 'permission', 'late']);
            $table->text('notes')->nullable();
            $table->timestamps();
            $table->unique(['student_id', 'teacher_attendance_id'], 'student_attendance_unique');
            $table->index('student_id', 'student_attendances_student_id_foreign');
            $table->index('teacher_attendance_id', 'student_attendances_teacher_attendance_id_foreign');
            $table->index('attendance_status', 'student_attendances_attendance_status_index');
        });
    }

    public function down(): void
    {
        Schema::dropIfExists('student_attendances');
    }
};
```

### 21. `create_geofence_logs_table.php`
```php
<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::create('geofence_logs', function (Blueprint $table) {
            $table->id();
            $table->foreignId('teacher_id')->constrained()->onDelete('cascade');
            $table->foreignId('geofence_id')->constrained()->onDelete('cascade');
            $table->enum('action', ['enter', 'exit']);
            $table->decimal('latitude', 10, 8);
            $table->decimal('longitude', 10, 8);
            $table->timestamp('logged_at')->useCurrent();
            $table->timestamps();
            $table->index('teacher_id', 'geofence_logs_teacher_id_foreign');
            $table->index('geofence_id', 'geofence_logs_geofence_id_foreign');
            $table->index('logged_at', 'geofence_logs_logged_at_index');
        });
    }

    public function down(): void
    {
        Schema::dropIfExists('geofence_logs');
    }
};
```

### 22. `create_teacher_salary_components_table.php`
```php
<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::create('teacher_salary_components', function (Blueprint $table) {
            $table->id();
            $table->foreignId('teacher_id')->constrained()->onDelete('cascade');
            $table->string('component_name');
            $table->decimal('amount', 15, 2);
            $table->enum('calculation_type', ['per_hour', 'fixed', 'percentage']);
            $table->date('effective_date');
            $table->date('end_date')->nullable();
            $table->timestamps();
            $table->index('teacher_id', 'teacher_salary_components_teacher_id_foreign');
            $table->index('effective_date', 'teacher_salary_components_effective_date_index');
        });
    }

    public function down(): void
    {
        Schema::dropIfExists('teacher_salary_components');
    }
};
```

### 23. `create_payroll_records_table.php`
```php
<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::create('payroll_records', function (Blueprint $table) {
            $table->id();
            $table->foreignId('teacher_id')->constrained()->onDelete('cascade');
            $table->foreignId('academic_year_id')->constrained();
            $table->string('period_name');
            $table->date('start_date');
            $table->date('end_date');
            $table->decimal('total_regular_teaching_hours', 8, 2)->default(0.00);
            $table->decimal('total_substitute_teaching_hours', 8, 2)->default(0.00);
            $table->enum('status', ['generated', 'paid', 'pending'])->default('generated');
            $table->foreignId('generated_by_user_id')->nullable()->constrained('users')->onDelete('set null');
            $table->timestamp('generated_at')->nullable();
            $table->timestamp('paid_at')->nullable();
            $table->timestamps();
            $table->unique(['teacher_id', 'period_name', 'academic_year_id'], 'payroll_records_unique_period');
            $table->index('teacher_id', 'payroll_records_teacher_id_foreign');
            $table->index('academic_year_id', 'payroll_records_academic_year_id_foreign');
            $table->index('generated_by_user_id', 'payroll_records_generated_by_user_id_foreign');
            $table->index('status', 'payroll_records_status_index');
            $table->index(['start_date', 'end_date'], 'payroll_records_start_date_end_date_index');
        });
    }

    public function down(): void
    {
        Schema::dropIfExists('payroll_records');
    }
};
```

---

## Models

### 1. `User.php`
```php
<?php

namespace App\Models;

use Illuminate\Foundation\Auth\User as Authenticatable;
use Illuminate\Notifications\Notifiable;
use Spatie\Permission\Traits\HasRoles;

class User extends Authenticatable
{
    use Notifiable, HasRoles;

    protected $fillable = [
        'username', 'name', 'email', 'photo_path', 'password', 'email_verified_at',
        'phone_number', 'status', 'last_login_at', 'remember_token',
    ];

    protected $casts = [
        'status' => 'boolean',
        'email_verified_at' => 'datetime',
        'last_login_at' => 'datetime',
    ];

    public function teacher()
    {
        return $this->hasOne(Teacher::class);
    }

    public function parent()
    {
        return $this->hasOne(Parent::class);
    }

    public function student()
    {
        return $this->hasOne(Student::class);
    }

    public function sessions()
    {
        return $this->hasMany(Session::class);
    }

    public function payrollRecords()
    {
        return $this->hasMany(PayrollRecord::class, 'generated_by_user_id');
    }
}
```

### 2. `PasswordResetToken.php`
```php
<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class PasswordResetToken extends Model
{
    protected $primaryKey = 'email';
    public $incrementing = false;
    protected $keyType = 'string';

    protected $fillable = [
        'email', 'token', 'created_at',
    ];

    protected $casts = [
        'created_at' => 'datetime',
    ];
}
```

### 3. `Session.php`
```php
<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class Session extends Model
{
    protected $primaryKey = 'id';
    public $incrementing = false;
    protected $keyType = 'string';

    protected $fillable = [
        'id', 'user_id', 'ip_address', 'user_agent', 'payload', 'last_activity',
    ];

    public function user()
    {
        return $this->belongsTo(User::class);
    }
}
```

### 4. `AcademicYear.php`
```php
<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class AcademicYear extends Model
{
    protected $fillable = [
        'name', 'semester', 'start_date', 'end_date', 'status',
    ];

    protected $casts = [
        'semester' => 'string',
        'status' => 'string',
        'start_date' => 'date',
        'end_date' => 'date',
    ];

    public function classrooms()
    {
        return $this->hasMany(Classroom::class);
    }

    public function classroomStudents()
    {
        return $this->hasMany(ClassroomStudent::class);
    }

    public function teacherAssignments()
    {
        return $this->hasMany(TeacherAssignment::class);
    }

    public function classSchedules()
    {
        return $this->hasMany(ClassSchedule::class);
    }

    public function payrollRecords()
    {
        return $this->hasMany(PayrollRecord::class);
    }
}
```

### 5. `Program.php`
```php
<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class Program extends Model
{
    protected $fillable = [
        'name', 'code', 'description', 'status',
    ];

    protected $casts = [
        'status' => 'string',
    ];

    public function classrooms()
    {
        return $this->hasMany(Classroom::class);
    }

    public function subjects()
    {
        return $this->hasMany(Subject::class);
    }
}
```

### 6. `Shift.php`
```php
<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class Shift extends Model
{
    protected $fillable = [
        'name', 'description', 'status',
    ];

    protected $casts = [
        'status' => 'string',
    ];

    public function classrooms()
    {
        return $this->hasMany(Classroom::class);
    }
}
```

### 7. `Classroom.php`
```php
<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class Classroom extends Model
{
    protected $fillable = [
        'name', 'level', 'capacity', 'program_id', 'shift_id', 'teacher_id', 'academic_year_id', 'status', 'description',
    ];

    protected $casts = [
        'level' => 'string',
        'status' => 'string',
    ];

    public function program()
    {
        return $this->belongsTo(Program::class);
    }

    public function shift()
    {
        return $this->belongsTo(Shift::class);
    }

    public function teacher()
    {
        return $this->belongsTo(Teacher::class);
    }

    public function academicYear()
    {
        return $this->belongsTo(AcademicYear::class);
    }

    public function classroomStudents()
    {
        return $this->hasMany(ClassroomStudent::class);
    }

    public function teacherAssignments()
    {
        return $this->hasMany(TeacherAssignment::class);
    }
}
```

### 8. `Subject.php`
```php
<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class Subject extends Model
{
    protected $fillable = [
        'name', 'program_id',
    ];

    public function program()
    {
        return $this->belongsTo(Program::class);
    }

    public function teacherAssignments()
    {
        return $this->hasMany(TeacherAssignment::class);
    }
}
```

### 9. `LessonHour.php`
```php
<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class LessonHour extends Model
{
    protected $fillable = [
        'name', 'start_time', 'end_time', 'sequence',
    ];

    protected $casts = [
        'start_time' => 'datetime:H:i:s',
        'end_time' => 'datetime:H:i:s',
    ];

    public function classSchedules()
    {
        return $this->hasMany(ClassSchedule::class);
    }
}
```

### 10. `Teacher.php`
```php
<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class Teacher extends Model
{
    protected $fillable = [
        'user_id', 'nip', 'birth_place', 'birth_date', 'gender', 'phone_number', 'full_address', 'gps_consent', 'gps_consent_given_at',
    ];

    protected $casts = [
        'gender' => 'string',
        'gps_consent' => 'boolean',
        'birth_date' => 'date',
        'gps_consent_given_at' => 'datetime',
    ];

    public function user()
    {
        return $this->belongsTo(User::class);
    }

    public function classrooms()
    {
        return $this->hasMany(Classroom::class);
    }

    public function teacherAssignments()
    {
        return $this->hasMany(TeacherAssignment::class);
    }

    public function teacherAttendances()
    {
        return $this->hasMany(TeacherAttendance::class);
    }

    public function originalTeacherAttendances()
    {
        return $this->hasMany(TeacherAttendance::class, 'original_teacher_id');
    }

    public function geofenceLogs()
    {
        return $this->hasMany(GeofenceLog::class);
    }

    public function teacherSalaryComponents()
    {
        return $this->hasMany(TeacherSalaryComponent::class);
    }

    public function payrollRecords()
    {
        return $this->hasMany(PayrollRecord::class);
    }
}
```

### 11. `Parent.php`
```php
<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class Parent extends Model
{
    protected $fillable = [
        'user_id', 'name', 'phone_number', 'occupation', 'address',
    ];

    public function user()
    {
        return $this->belongsTo(User::class);
    }

    public function studentParents()
    {
        return $this->hasMany(StudentParent::class);
    }
}
```

### 12. `Student.php`
```php
<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class Student extends Model
{
    protected $fillable = [
        'user_id', 'nis', 'nisn', 'birth_place', 'birth_date', 'gender', 'religion', 'address', 'entry_year', 'profile_picture', 'phone',
    ];

    protected $casts = [
        'gender' => 'string',
        'birth_date' => 'date',
        'entry_year' => 'integer',
    ];

    public function user()
    {
        return $this->belongsTo(User::class);
    }

    public function studentParents()
    {
        return $this->hasMany(StudentParent::class);
    }

    public function classroomStudents()
    {
        return $this->hasMany(ClassroomStudent::class);
    }

    public function studentAttendances()
    {
        return $this->hasMany(StudentAttendance::class);
    }
}
```

### 13. `StudentParent.php`
```php
<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class StudentParent extends Model
{
    protected $fillable = [
        'student_id', 'parent_id', 'relation_type',
    ];

    public function student()
    {
        return $this->belongsTo(Student::class);
    }

    public function parent()
    {
        return $this->belongsTo(Parent::class);
    }
}
```

### 14. `ClassroomStudent.php`
```php
<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class ClassroomStudent extends Model
{
    protected $fillable = [
        'classroom_id', 'student_id', 'academic_year_id',
    ];

    public function classroom()
    {
        return $this->belongsTo(Classroom::class);
    }

    public function student()
    {
        return $this->belongsTo(Student::class);
    }

    public function academicYear()
    {
        return $this->belongsTo(AcademicYear::class);
    }
}
```

### 15. `TeacherAssignment.php`
```php
<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class TeacherAssignment extends Model
{
    protected $fillable = [
        'teacher_id', 'subject_id', 'classroom_id', 'academic_year_id', 'is_homeroom_teacher',
    ];

    protected $casts = [
        'is_homeroom_teacher' => 'boolean',
    ];

    public function teacher()
    {
        return $this->belongsTo(Teacher::class);
    }

    public function subject()
    {
        return $this->belongsTo(Subject::class);
    }

    public function classroom()
    {
        return $this->belongsTo(Classroom::class);
    }

    public function academicYear()
    {
        return $this->belongsTo(AcademicYear::class);
    }

    public function classSchedules()
    {
        return $this->hasMany(ClassSchedule::class);
    }
}
```

### 16. `ClassSchedule.php`
```php
<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class ClassSchedule extends Model
{
    protected $fillable = [
        'teacher_assignment_id', 'lesson_hour_id', 'day_of_week', 'academic_year_id',
    ];

    protected $casts = [
        'day_of_week' => 'string',
    ];

    public function teacherAssignment()
    {
        return $this->belongsTo(TeacherAssignment::class);
    }

    public function lessonHour()
    {
        return $this->belongsTo(LessonHour::class);
    }

    public function academicYear()
    {
        return $this->belongsTo(AcademicYear::class);
    }

    public function scheduleExceptions()
    {
        return $this->hasMany(ScheduleException::class);
    }

    public function teacherAttendances()
    {
        return $this->hasMany(TeacherAttendance::class);
    }
}
```

### 17. `ScheduleException.php`
```php
<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class ScheduleException extends Model
{
    protected $fillable = [
        'class_schedule_id', 'exception_date', 'reason',
    ];

    protected $casts = [
        'exception_date' => 'date',
    ];

    public function classSchedule()
    {
        return $this->belongsTo(ClassSchedule::class);
    }
}
```

### 18. `Geofence.php`
```php
<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class Geofence extends Model
{
    protected $fillable = [
        'name', 'latitude', 'longitude', 'radius',
    ];

    protected $casts = [
        'latitude' => 'decimal:8',
        'longitude' => 'decimal:8',
        'radius' => 'decimal:2',
    ];

    public function teacherAttendances()
    {
        return $this->hasMany(TeacherAttendance::class);
    }

    public function geofenceLogs()
    {
        return $this->hasMany(GeofenceLog::class);
    }
}
```

### 19. `TeacherAttendance.php`
```php
<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class TeacherAttendance extends Model
{
    protected $fillable = [
        'teacher_id', 'class_schedule_id', 'attendance_date', 'check_in_time', 'check_out_time',
        'latitude', 'longitude', 'geofence_id', 'status', 'is_substitute_teacher', 'original_teacher_id', 'notes',
    ];

    protected $casts = [
        'attendance_date' => 'date',
        'check_in_time' => 'datetime',
        'check_out_time' => 'datetime',
        'latitude' => 'decimal:8',
        'longitude' => 'decimal:8',
        'status' => 'string',
        'is_substitute_teacher' => 'boolean',
    ];

    public function teacher()
    {
        return $this->belongsTo(Teacher::class);
    }

    public function classSchedule()
    {
        return $this->belongsTo(ClassSchedule::class);
    }

    public function geofence()
    {
        return $this->belongsTo(Geofence::class);
    }

    public function originalTeacher()
    {
        return $this->belongsTo(Teacher::class, 'original_teacher_id');
    }

    public function studentAttendances()
    {
        return $this->hasMany(StudentAttendance::class);
    }
}
```

### 20. `StudentAttendance.php`
```php
<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class StudentAttendance extends Model
{
    protected $fillable = [
        'student_id', 'teacher_attendance_id', 'attendance_status', 'notes',
    ];

    protected $casts = [
        'attendance_status' => 'string',
    ];

    public function student()
    {
        return $this->belongsTo(Student::class);
    }

    public function teacherAttendance()
    {
        return $this->belongsTo(TeacherAttendance::class);
    }
}
```

### 21. `GeofenceLog.php`
```php
<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class GeofenceLog extends Model
{
    protected $fillable = [
        'teacher_id', 'geofence_id', 'action', 'latitude', 'longitude', 'logged_at',
    ];

    protected $casts = [
        'action' => 'string',
        'latitude' => 'decimal:8',
        'longitude' => 'decimal:8',
        'logged_at' => 'datetime',
    ];

    public function teacher()
    {
        return $this->belongsTo(Teacher::class);
    }

    public function geofence()
    {
        return $this->belongsTo(Geofence::class);
    }
}
```

### 22. `TeacherSalaryComponent.php`
```php
<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class TeacherSalaryComponent extends Model
{
    protected $fillable = [
        'teacher_id', 'component_name', 'amount', 'calculation_type', 'effective_date', 'end_date',
    ];

    protected $casts = [
        'amount' => 'decimal:2',
        'calculation_type' => 'string',
        'effective_date' => 'date',
        'end_date' => 'date',
    ];

    public function teacher()
    {
        return $this->belongsTo(Teacher::class);
    }
}
```

### 23. `PayrollRecord.php`
```php
<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class PayrollRecord extends Model
{
    protected $fillable = [
        'teacher_id', 'academic_year_id', 'period_name', 'start_date', 'end_date',
        'total_regular_teaching_hours', 'total_substitute_teaching_hours', 'status',
        'generated_by_user_id', 'generated_at', 'paid_at',
    ];

    protected $casts = [
        'start_date' => 'date',
        'end_date' => 'date',
        'total_regular_teaching_hours' => 'decimal:2',
        'total_substitute_teaching_hours' => 'decimal:2',
        'status' => 'string',
        'generated_at' => 'datetime',
        'paid_at' => 'datetime',
    ];

    public function teacher()
    {
        return $this->belongsTo(Teacher::class);
    }

    public function academicYear()
    {
        return $this->belongsTo(AcademicYear::class);
    }

    public function generatedBy()
    {
        return $this->belongsTo(User::class, 'generated_by_user_id');
    }
}
```

---

## Notes
- **Migration Order**: Ensure migrations are run in the correct order due to foreign key dependencies:
  1. `users`, `academic_years`, `programs`, `shifts`
  2. `teachers`, `subjects`, `lesson_hours`
  3. `classrooms`
  4. `parents`, `students`
  5. `student_parents`, `classroom_students`, `teacher_assignments`
  6. `class_schedules`, `geofences`
  7. `schedule_exceptions`, `teacher_attendances`
  8. `student_attendances`, `geofence_logs`, `teacher_salary_components`, `payroll_records`
- **Spatie Permissions**: The `User` model uses the `HasRoles` trait for Spatie's permission package. Ensure the Spatie migration for permissions is included separately.
- **Casts**: Models include casts for enums, dates, decimals, and booleans to ensure proper type handling.
- **Fillable Fields**: All fillable fields are defined to prevent mass assignment vulnerabilities.
- **Relationships**: Models include Eloquent relationships (`belongsTo`, `hasMany`, `hasOne`) to reflect the database structure.
- **File Organization**: Place migrations in the `database/migrations` directory and models in the `app/Models` directory.

This setup provides a complete Laravel implementation of the `db_rawooh_v2` database schema.