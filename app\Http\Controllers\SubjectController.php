<?php

namespace App\Http\Controllers;

use App\Http\Requests\Subject\StoreSubjectRequest;
use App\Http\Requests\Subject\UpdateSubjectRequest;
use App\Models\Subject;
use Illuminate\Http\RedirectResponse;
use Illuminate\Http\Request;
use Illuminate\View\View;

class SubjectController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index(Request $request): View
    {
        $query = Subject::with(['teacherAssignments']);

        // Apply search if provided
        if ($request->filled('search')) {
            $search = $request->get('search');
            $query->where(function ($q) use ($search) {
                $q->where('name', 'like', "%{$search}%")
                    ->orWhere('code', 'like', "%{$search}%")
                    ->orWhere('description', 'like', "%{$search}%");
            });
        }

        // Apply status filter if provided
        if ($request->filled('status')) {
            $query->where('status', $request->get('status'));
        }

        // Apply credit hours filter if provided
        if ($request->filled('credit_hours')) {
            $query->where('credit_hours', $request->get('credit_hours'));
        }

        // Apply sorting
        $sortBy = $request->get('sort_by', 'name');
        $sortDirection = $request->get('sort_direction', 'asc');
        $query->orderBy($sortBy, $sortDirection);

        $subjects = $query->paginate(15);
        $subjects->appends($request->query());

        return view('subjects.index', compact('subjects'));
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create(): View
    {
        return view('subjects.create');
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(StoreSubjectRequest $request): RedirectResponse
    {
        try {
            $data = $request->validated();

            // Set default status if not provided
            if (!isset($data['status'])) {
                $data['status'] = 'active';
            }

            $subject = Subject::create($data);

            // Log subject creation
            logger()->info('Subject created', [
                'subject_id' => $subject->id,
                'code' => $subject->code,
                'name' => $subject->name,
                'credit_hours' => $subject->credit_hours,
                'created_by' => auth()->id(),
            ]);

            return redirect()
                ->route('subjects.show', $subject)
                ->with('success', 'Subject created successfully.');
        } catch (\Exception $e) {
            return back()
                ->withInput()
                ->with('error', 'Failed to create subject: ' . $e->getMessage());
        }
    }

    /**
     * Display the specified resource.
     */
    public function show(Subject $subject): View
    {
        $subject->load(['teacherAssignments.teacher.user', 'teacherAssignments.classroom']);

        return view('subjects.show', compact('subject'));
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(Subject $subject): View
    {
        return view('subjects.edit', compact('subject'));
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(UpdateSubjectRequest $request, Subject $subject): RedirectResponse
    {
        try {
            $data = $request->validated();

            $subject->update($data);

            // Log subject update
            logger()->info('Subject updated', [
                'subject_id' => $subject->id,
                'code' => $subject->code,
                'name' => $subject->name,
                'credit_hours' => $subject->credit_hours,
                'updated_by' => auth()->id(),
            ]);

            return redirect()
                ->route('subjects.show', $subject)
                ->with('success', 'Subject updated successfully.');
        } catch (\Exception $e) {
            return back()
                ->withInput()
                ->with('error', 'Failed to update subject: ' . $e->getMessage());
        }
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(Subject $subject): RedirectResponse
    {
        try {
            // Check if subject has active teacher assignments
            if ($subject->teacherAssignments()->exists()) {
                return back()->with('error', 'Cannot delete subject with active teacher assignments.');
            }

            // Log subject deletion before deleting
            logger()->info('Subject deleted', [
                'subject_id' => $subject->id,
                'code' => $subject->code,
                'name' => $subject->name,
                'deleted_by' => auth()->id(),
            ]);

            $subject->delete();

            return redirect()
                ->route('subjects.index')
                ->with('success', 'Subject deleted successfully.');
        } catch (\Exception $e) {
            return back()
                ->with('error', 'Failed to delete subject: ' . $e->getMessage());
        }
    }
}
