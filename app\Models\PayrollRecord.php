<?php

namespace App\Models;

use App\Enums\PayrollStatus;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class PayrollRecord extends Model
{
    protected $fillable = [
        'teacher_id',
        'payroll_period',
        'period_start',
        'period_end',
        'basic_salary',
        'total_allowances',
        'total_deductions',
        'total_bonuses',
        'gross_salary',
        'tax_amount',
        'net_salary',
        'total_working_days',
        'total_present_days',
        'status',
        'payment_date',
        'notes'
    ];

    protected $casts = [
        'status' => PayrollStatus::class,
        'period_start' => 'date',
        'period_end' => 'date',
        'payment_date' => 'date',
        'basic_salary' => 'decimal:2',
        'total_allowances' => 'decimal:2',
        'total_deductions' => 'decimal:2',
        'total_bonuses' => 'decimal:2',
        'gross_salary' => 'decimal:2',
        'tax_amount' => 'decimal:2',
        'net_salary' => 'decimal:2',
        'total_working_days' => 'integer',
        'total_present_days' => 'integer',
    ];

    public function teacher(): BelongsTo
    {
        return $this->belongsTo(Teacher::class);
    }

    public function scopePaid($query)
    {
        return $query->where('status', PayrollStatus::Paid);
    }

    public function scopeForPeriod($query, $period)
    {
        return $query->where('payroll_period', $period);
    }
}
